#!/usr/bin/env python3
"""
文件加载工作线程模块
"""

from pathlib import Path
from PySide6.QtCore import QObject, QRunnable, Signal

from core.file_loader import FileLoader
from utils.constants import SUPPORTED_IMAGE_FORMATS, SUPPORTED_PDF_FORMATS, SUPPORTED_DXF_FORMATS

class FileLoaderSignals(QObject):
    """文件加载工作线程的信号类"""
    finished = Signal(str, object)  # 成功加载后发出信号：文件路径, 图像数据
    pdf_loaded = Signal(str, int)  # PDF加载成功的信号：文件路径, 页数
    error = Signal(str)  # 加载出错时发出信号
    progress = Signal(int, str)  # 加载进度信号：进度值, 描述

class FileLoaderWorker(QRunnable):
    """文件加载工作线程"""
    def __init__(self, file_path, pdf_quality="高清 (4x)"):
        super().__init__()
        self.file_path = file_path
        self.pdf_quality = pdf_quality
        self.signals = FileLoaderSignals()
        
    def run(self):
        """执行文件加载"""
        try:
            file_path = Path(self.file_path)
            extension = file_path.suffix.lower()

            self.signals.progress.emit(10, f"正在加载文件 {file_path.name}...")

            # 处理图像文件
            if extension in SUPPORTED_IMAGE_FORMATS:
                self.signals.progress.emit(30, f"正在加载图像 {file_path.name}...")
                pixmap = FileLoader.load_image(str(file_path))
                if pixmap:
                    self.signals.progress.emit(90, "图像加载成功")
                    self.signals.finished.emit(str(file_path), pixmap)
                else:
                    self.signals.error.emit(f"无法加载图像文件: {file_path.name}")
                    
            # 处理PDF文件
            elif extension in SUPPORTED_PDF_FORMATS:
                self.signals.progress.emit(20, f"正在分析PDF文件 {file_path.name}...")
                
                # 获取PDF页数
                page_count = FileLoader.get_pdf_page_count(str(file_path))
                if page_count == 0:
                    self.signals.error.emit("无法读取PDF文件或PDF文件不包含任何页面")
                    return
                
                # 通知PDF加载成功
                self.signals.progress.emit(80, f"PDF文件加载成功，共 {page_count} 页")
                self.signals.pdf_loaded.emit(str(file_path), page_count)
                    
            # 处理DXF文件
            elif extension in SUPPORTED_DXF_FORMATS:
                self.signals.error.emit("DXF文件加载尚未实现多线程支持")
                
            else:
                self.signals.error.emit(f"不支持的文件格式: {extension}")
                
        except Exception as e:
            self.signals.error.emit(f"加载文件时发生错误: {str(e)}")
