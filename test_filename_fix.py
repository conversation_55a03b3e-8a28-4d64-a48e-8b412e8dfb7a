#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件名修复功能的脚本
验证在PDF模式下导出功能是否使用正确的文件名
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_filename_logic():
    """测试文件名生成逻辑"""
    print("=== 测试文件名生成逻辑 ===")
    
    # 模拟PDF模式
    pdf_file_path = "C:/Users/<USER>/Documents/机械图纸.pdf"
    current_file_path = "C:/Temp/pdf_convert_abc123_page0.png"
    
    print(f"原始PDF路径: {pdf_file_path}")
    print(f"临时文件路径: {current_file_path}")
    
    # 测试修改后的逻辑
    if pdf_file_path:
        # PDF模式下使用原始PDF文件名
        pdf_name = Path(pdf_file_path).stem
        default_filename_excel = f"{pdf_name}_标注列表.xlsx"
        default_filename_template = f"{pdf_name}_检验报告.xlsx"
        print(f"\n✅ PDF模式 - Excel导出文件名: {default_filename_excel}")
        print(f"✅ PDF模式 - 模板导出文件名: {default_filename_template}")
    elif current_file_path:
        # 普通图片模式使用当前文件名
        default_filename_excel = f"{Path(current_file_path).stem}_标注列表.xlsx"
        default_filename_template = f"{Path(current_file_path).stem}_检验报告.xlsx"
        print(f"\n📷 图片模式 - Excel导出文件名: {default_filename_excel}")
        print(f"📷 图片模式 - 模板导出文件名: {default_filename_template}")
    else:
        default_filename_excel = "标注列表.xlsx"
        default_filename_template = "检验报告.xlsx"
        print(f"\n⚠️  默认模式 - Excel导出文件名: {default_filename_excel}")
        print(f"⚠️  默认模式 - 模板导出文件名: {default_filename_template}")
    
    # 测试修改前的逻辑（错误的）
    print(f"\n❌ 修改前的错误逻辑:")
    old_filename_excel = f"{Path(current_file_path).stem}_标注列表.xlsx"
    old_filename_template = f"{Path(current_file_path).stem}_检验报告.xlsx"
    print(f"   Excel导出文件名: {old_filename_excel}")
    print(f"   模板导出文件名: {old_filename_template}")
    
    print("\n=== 测试完成 ===")
    print("✅ 修改后的逻辑正确使用了原始PDF文件名")
    print("❌ 修改前的逻辑错误使用了临时文件名")

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    # 测试1: 只有PDF路径
    pdf_file_path = "测试文档.pdf"
    current_file_path = None
    
    if pdf_file_path:
        pdf_name = Path(pdf_file_path).stem
        filename = f"{pdf_name}_标注列表.xlsx"
        print(f"情况1 - 只有PDF路径: {filename}")
    
    # 测试2: 只有图片路径
    pdf_file_path = None
    current_file_path = "测试图片.png"
    
    if pdf_file_path:
        pass
    elif current_file_path:
        filename = f"{Path(current_file_path).stem}_标注列表.xlsx"
        print(f"情况2 - 只有图片路径: {filename}")
    
    # 测试3: 都没有
    pdf_file_path = None
    current_file_path = None
    
    if pdf_file_path:
        pass
    elif current_file_path:
        pass
    else:
        filename = "标注列表.xlsx"
        print(f"情况3 - 都没有: {filename}")
    
    print("✅ 边界情况测试通过")

if __name__ == "__main__":
    test_filename_logic()
    test_edge_cases()
    
    print("\n🎉 所有测试完成！修改后的代码应该能正确处理文件名。")
    print("\n📝 建议:")
    print("1. 在实际应用中测试PDF识别和导出功能")
    print("2. 验证导出的文件名确实使用了原始PDF文件名")
    print("3. 确保普通图片模式仍然正常工作")
