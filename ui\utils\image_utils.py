#!/usr/bin/env python3
"""
图像处理工具模块
"""

import os
from typing import <PERSON><PERSON>, Optional, Dict, Any
from PySide6.QtGui import QPixmap, QImage
from PySide6.QtCore import QSize

def create_thumbnail(pixmap: QPixmap, size: QSize) -> QPixmap:
    """创建缩略图"""
    if pixmap.isNull():
        return QPixmap()
    
    return pixmap.scaled(
        size, 
        aspectRatioMode=1,  # Qt.KeepAspectRatio
        transformMode=1     # Qt.SmoothTransformation
    )

def calculate_scale_factor(image_size: QSize, target_size: QSize) -> float:
    """计算缩放因子以适应目标尺寸"""
    if image_size.isEmpty() or target_size.isEmpty():
        return 1.0
    
    width_ratio = target_size.width() / image_size.width()
    height_ratio = target_size.height() / image_size.height()
    
    # 使用较小的比例以确保图像完全适应
    return min(width_ratio, height_ratio)

def get_image_info(file_path: str) -> Dict[str, Any]:
    """获取图像文件信息"""
    info = {
        'width': 0,
        'height': 0,
        'format': '',
        'size_bytes': 0,
        'exists': False,
        'valid': False
    }
    
    if not os.path.exists(file_path):
        return info
    
    info['exists'] = True
    info['size_bytes'] = os.path.getsize(file_path)
    
    # 尝试加载图像获取尺寸信息
    image = QImage(file_path)
    if not image.isNull():
        info['width'] = image.width()
        info['height'] = image.height()
        info['format'] = image.format()
        info['valid'] = True
    
    return info

def estimate_memory_usage(width: int, height: int, bytes_per_pixel: int = 4) -> int:
    """估算图像内存使用量（字节）"""
    return width * height * bytes_per_pixel

def is_large_image(file_path: str, threshold_mb: int = 50) -> bool:
    """判断是否为大图像文件"""
    info = get_image_info(file_path)
    if not info['valid']:
        return False
    
    estimated_memory = estimate_memory_usage(info['width'], info['height'])
    threshold_bytes = threshold_mb * 1024 * 1024
    
    return estimated_memory > threshold_bytes

def calculate_optimal_size(original_size: QSize, max_memory_mb: int = 100) -> QSize:
    """计算最优显示尺寸以控制内存使用"""
    if original_size.isEmpty():
        return QSize()
    
    max_memory_bytes = max_memory_mb * 1024 * 1024
    original_memory = estimate_memory_usage(original_size.width(), original_size.height())
    
    if original_memory <= max_memory_bytes:
        return original_size
    
    # 计算缩放因子
    scale_factor = (max_memory_bytes / original_memory) ** 0.5
    
    new_width = int(original_size.width() * scale_factor)
    new_height = int(original_size.height() * scale_factor)
    
    return QSize(new_width, new_height)

def get_supported_formats() -> list:
    """获取支持的图像格式列表"""
    return [
        'png', 'jpg', 'jpeg', 'bmp', 'gif', 'tiff', 'tif',
        'webp', 'ico', 'svg', 'svgz'
    ]

def is_supported_image_format(file_path: str) -> bool:
    """检查文件是否为支持的图像格式"""
    if not file_path:
        return False
    
    extension = os.path.splitext(file_path)[1].lower().lstrip('.')
    return extension in get_supported_formats()

def validate_image_file(file_path: str) -> Tuple[bool, str]:
    """验证图像文件
    
    Returns:
        Tuple[bool, str]: (是否有效, 错误信息)
    """
    if not file_path:
        return False, "文件路径为空"
    
    if not os.path.exists(file_path):
        return False, "文件不存在"
    
    if not is_supported_image_format(file_path):
        return False, "不支持的图像格式"
    
    info = get_image_info(file_path)
    if not info['valid']:
        return False, "无法读取图像文件"
    
    if info['width'] <= 0 or info['height'] <= 0:
        return False, "图像尺寸无效"
    
    return True, ""

def create_scaled_pixmap(file_path: str, max_size: QSize) -> QPixmap:
    """创建缩放后的QPixmap"""
    pixmap = QPixmap(file_path)
    if pixmap.isNull():
        return QPixmap()
    
    if pixmap.size().width() <= max_size.width() and pixmap.size().height() <= max_size.height():
        return pixmap
    
    return pixmap.scaled(
        max_size,
        aspectRatioMode=1,  # Qt.KeepAspectRatio
        transformMode=1     # Qt.SmoothTransformation
    )

def get_image_aspect_ratio(width: int, height: int) -> float:
    """获取图像宽高比"""
    if height == 0:
        return float('inf')
    return width / height

def is_landscape_image(width: int, height: int) -> bool:
    """判断是否为横向图像"""
    return width > height

def is_portrait_image(width: int, height: int) -> bool:
    """判断是否为纵向图像"""
    return height > width

def is_square_image(width: int, height: int, tolerance: float = 0.1) -> bool:
    """判断是否为正方形图像（允许一定误差）"""
    if width == 0 or height == 0:
        return False
    
    ratio = width / height
    return abs(ratio - 1.0) <= tolerance

def calculate_fit_size(image_size: QSize, container_size: QSize) -> QSize:
    """计算图像适应容器的尺寸"""
    if image_size.isEmpty() or container_size.isEmpty():
        return QSize()
    
    scale_factor = calculate_scale_factor(image_size, container_size)
    
    return QSize(
        int(image_size.width() * scale_factor),
        int(image_size.height() * scale_factor)
    )

def get_file_size_mb(file_path: str) -> float:
    """获取文件大小（MB）"""
    if not os.path.exists(file_path):
        return 0.0
    
    size_bytes = os.path.getsize(file_path)
    return size_bytes / (1024 * 1024)

def should_downsample_image(file_path: str, max_memory_mb: int = 100) -> bool:
    """判断是否需要降采样图像"""
    info = get_image_info(file_path)
    if not info['valid']:
        return False
    
    estimated_memory_mb = estimate_memory_usage(info['width'], info['height']) / (1024 * 1024)
    return estimated_memory_mb > max_memory_mb

def create_error_pixmap(size: QSize, message: str = "加载失败") -> QPixmap:
    """创建错误提示图像"""
    pixmap = QPixmap(size)
    pixmap.fill()  # 填充白色背景
    
    # 这里可以添加绘制错误信息的代码
    # 暂时返回空白图像
    return pixmap
