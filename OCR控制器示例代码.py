#!/usr/bin/env python3
"""
OCR控制器示例 - 展示如何从MainWindow中提取OCR功能
"""

import tempfile
from typing import List, Dict, Optional
from PySide6.QtCore import QObject, Signal, QRectF, QPointF
from PySide6.QtWidgets import QMessageBox

from utils.constants import DEFAULT_OCR_LANGUAGES
from utils.dependencies import HAS_OCR_SUPPORT, HAS_PADDLE_OCR

if HAS_PADDLE_OCR:
    from core.paddle_ocr_worker import PaddleOCRWorker

class OCRController(QObject):
    """OCR控制器 - 负责OCR识别流程控制和结果处理"""
    
    # 信号定义
    ocr_started = Signal()
    ocr_progress = Signal(int)
    ocr_finished = Signal(list)  # OCR结果列表
    ocr_error = Signal(str)
    
    area_ocr_started = Signal(QRectF)
    area_ocr_finished = Signal(list, QRectF, str, int, int)  # 结果、区域、临时路径、偏移
    area_ocr_error = Signal(str)
    
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.ocr_worker = None
        self.area_ocr_worker = None
        self.ocr_results = []
        
        # 连接UI信号
        self._connect_ui_signals()
    
    def _connect_ui_signals(self):
        """连接UI相关信号"""
        # 连接进度更新
        self.ocr_progress.connect(self.main_window.progress_bar.setValue)
        
        # 连接按钮状态更新
        self.ocr_started.connect(self._on_ocr_started)
        self.ocr_finished.connect(self._on_ocr_finished)
        self.ocr_error.connect(self._on_ocr_error)
    
    def start_recognition(self):
        """启动全局OCR识别"""
        if not HAS_OCR_SUPPORT:
            QMessageBox.warning(
                self.main_window, 
                "功能缺失", 
                "OCR功能需要PaddleOCR和依赖包。请安装所需依赖。"
            )
            return
        
        if not self.main_window.current_pixmap:
            QMessageBox.information(self.main_window, "提示", "请先打开图片文件。")
            return
        
        # 保存已有结果
        existing_results = self.ocr_results.copy()
        
        # 清除显示
        self.clear_ocr_display()
        
        # 获取配置
        config = self._get_ocr_config()
        
        # 创建OCR工作器
        self.ocr_worker = PaddleOCRWorker(
            self.main_window.current_file_path,
            config['languages'],
            config['masked_regions'],
            force_cpu=config['force_cpu'],
            cpu_threads=config['cpu_threads'],
            direct_recognition=False
        )
        
        # 连接信号
        self.ocr_worker.signals.progress.connect(self.ocr_progress.emit)
        self.ocr_worker.signals.error.connect(self.ocr_error.emit)
        self.ocr_worker.signals.finished.connect(
            lambda results: self._handle_ocr_finished(results, existing_results)
        )
        
        # 发送开始信号
        self.ocr_started.emit()
        
        # 启动线程
        self.main_window.thread_pool.start(self.ocr_worker)
    
    def start_area_recognition(self, rect: QRectF):
        """启动区域OCR识别"""
        if not HAS_OCR_SUPPORT:
            self.area_ocr_error.emit("OCR功能不可用")
            return
        
        if not self.main_window.current_pixmap:
            self.area_ocr_error.emit("请先打开图片文件")
            return
        
        # 计算区域参数
        offset_x = int(rect.x())
        offset_y = int(rect.y())
        width = int(rect.width())
        height = int(rect.height())
        
        # 判断是否为竖排文本
        aspect_ratio = height / max(width, 1)
        is_vertical = aspect_ratio > 1.2
        
        # 裁剪图像
        cropped_pixmap = self.main_window.current_pixmap.copy(
            offset_x, offset_y, width, height
        )
        
        # 保存临时文件
        temp_path = tempfile.mktemp(suffix='.png')
        cropped_pixmap.save(temp_path)
        
        # 获取配置
        config = self._get_ocr_config()
        
        # 创建区域OCR工作器
        self.area_ocr_worker = PaddleOCRWorker(
            temp_path,
            config['languages'],
            [],  # 区域OCR不使用屏蔽区域
            force_cpu=config['force_cpu'],
            cpu_threads=config['cpu_threads'],
            direct_recognition=True,
            is_area_ocr=True
        )
        
        # 连接信号
        self.area_ocr_worker.signals.finished.connect(
            lambda results: self.area_ocr_finished.emit(
                results, rect, temp_path, offset_x, offset_y
            )
        )
        self.area_ocr_worker.signals.error.connect(self.area_ocr_error.emit)
        
        # 发送开始信号
        self.area_ocr_started.emit(rect)
        
        # 启动线程
        self.main_window.thread_pool.start(self.area_ocr_worker)
    
    def _get_ocr_config(self) -> Dict:
        """获取OCR配置"""
        # 获取语言配置
        lang_text = self.main_window.language_combo.currentText()
        languages = DEFAULT_OCR_LANGUAGES.get(lang_text, ["ch_sim"])
        
        # 获取环境配置
        force_cpu = self.main_window.cpu_checkbox.isChecked()
        cpu_threads = self.main_window.threads_spinbox.value()
        
        # 获取屏蔽区域
        masked_regions = [
            {
                'x': r.x(), 
                'y': r.y(), 
                'width': r.width(), 
                'height': r.height()
            } 
            for r in self.main_window.masked_regions
        ]
        
        return {
            'languages': languages,
            'force_cpu': force_cpu,
            'cpu_threads': cpu_threads,
            'masked_regions': masked_regions
        }
    
    def _handle_ocr_finished(self, results: List[dict], existing_results: List[dict]):
        """处理OCR完成"""
        # 合并结果
        if existing_results:
            # 合并相邻结果
            merged_results = self.merge_adjacent_ocr_results(results)
            all_results = existing_results + merged_results
        else:
            all_results = self.merge_adjacent_ocr_results(results)
        
        # 更新结果
        self.ocr_results = all_results
        
        # 显示结果
        self.display_ocr_results()
        
        # 更新统计
        self.update_ocr_stats()
        
        # 发送完成信号
        self.ocr_finished.emit(all_results)
    
    def merge_adjacent_ocr_results(self, results: List[dict]) -> List[dict]:
        """合并相邻的OCR结果"""
        if not results or len(results) <= 1:
            return results
        
        # 按坐标排序
        sorted_results = sorted(
            results, 
            key=lambda r: (r.get('center_y', 0), r.get('center_x', 0))
        )
        
        # 实现合并逻辑
        # (这里可以从原始MainWindow中复制merge_adjacent_ocr_results的逻辑)
        
        return sorted_results
    
    def classify_ocr_results(self, results: List[dict]):
        """对OCR结果进行分类"""
        for result in results:
            text = result.get('text', '').strip()
            
            # 实现文本分类逻辑
            if self._is_dimension_text(text):
                result['type'] = 'dimension'
            elif self._is_tolerance_text(text):
                result['type'] = 'tolerance'
            elif self._is_thread_spec_text(text):
                result['type'] = 'thread_spec'
            else:
                result['type'] = 'annotation'
    
    def _is_dimension_text(self, text: str) -> bool:
        """判断是否为尺寸文本"""
        # 实现尺寸文本判断逻辑
        import re
        dimension_patterns = [
            r'^\d+(\.\d+)?$',  # 纯数字
            r'^\d+(\.\d+)?mm$',  # 带mm单位
            r'^Ø\d+(\.\d+)?$',  # 直径标注
            r'^R\d+(\.\d+)?$',  # 半径标注
        ]
        
        return any(re.match(pattern, text) for pattern in dimension_patterns)
    
    def _is_tolerance_text(self, text: str) -> bool:
        """判断是否为公差文本"""
        import re
        tolerance_patterns = [
            r'^[+-]\d+(\.\d+)?$',  # ±数值
            r'^±\d+(\.\d+)?$',     # ±数值
            r'^[+-]0\.\d+$',       # ±0.xxx
        ]
        
        return any(re.match(pattern, text) for pattern in tolerance_patterns)
    
    def _is_thread_spec_text(self, text: str) -> bool:
        """判断是否为螺纹规格文本"""
        import re
        thread_patterns = [
            r'^M\d+$',           # M8, M10等
            r'^M\d+×\d+(\.\d+)?$',  # M8×1.25等
            r'^M\d+x\d+(\.\d+)?$',  # M8x1.25等
        ]
        
        return any(re.match(pattern, text) for pattern in thread_patterns)
    
    def filter_ocr_results(self):
        """筛选OCR结果"""
        filter_type = self.main_window.filter_combo.currentText()
        
        if filter_type == "全部":
            filtered_results = self.ocr_results
        else:
            # 根据筛选类型过滤结果
            filtered_results = [
                result for result in self.ocr_results
                if result.get('type') == filter_type.lower()
            ]
        
        # 更新显示
        self.display_filtered_results(filtered_results)
    
    def display_ocr_results(self):
        """显示OCR结果"""
        self.clear_ocr_display()
        
        for i, result in enumerate(self.ocr_results):
            self.create_ocr_bbox_item(result, i)
    
    def display_filtered_results(self, results: List[dict]):
        """显示筛选后的结果"""
        self.clear_ocr_display()
        
        for i, result in enumerate(results):
            self.create_ocr_bbox_item(result, i)
    
    def create_ocr_bbox_item(self, ocr_result: dict, index: int):
        """创建OCR边界框显示项"""
        # 实现边界框显示逻辑
        # (从原始MainWindow中复制相关代码)
        pass
    
    def clear_ocr_display(self):
        """清除OCR显示"""
        # 清除场景中的OCR显示项
        scene = self.main_window.graphics_scene
        items_to_remove = []
        
        for item in scene.items():
            if hasattr(item, 'is_ocr_bbox') and item.is_ocr_bbox:
                items_to_remove.append(item)
        
        for item in items_to_remove:
            scene.removeItem(item)
    
    def clear_ocr_results(self):
        """清除OCR结果"""
        self.ocr_results.clear()
        self.clear_ocr_display()
        self.update_ocr_stats()
    
    def update_ocr_stats(self):
        """更新OCR统计信息"""
        total_count = len(self.ocr_results)
        self.main_window.ocr_stats_label.setText(f"识别结果: {total_count}个文本")
    
    def _on_ocr_started(self):
        """OCR开始时的UI更新"""
        self.main_window.ocr_button.setEnabled(False)
        self.main_window.ocr_button.setText("🔄 识别中...")
        self.main_window.progress_bar.setVisible(True)
        self.main_window.progress_bar.setValue(0)
        
        device_mode = "CPU" if self.main_window.cpu_checkbox.isChecked() else "GPU"
        self.main_window.status_bar.showMessage(f"正在进行OCR文本识别... (使用{device_mode}模式)")
    
    def _on_ocr_finished(self, results: List[dict]):
        """OCR完成时的UI更新"""
        self.main_window.ocr_button.setEnabled(True)
        self.main_window.ocr_button.setText("🔍 开始OCR识别")
        self.main_window.progress_bar.setVisible(False)
        
        result_count = len(results)
        self.main_window.status_bar.showMessage(f"✅ OCR识别完成，共识别到 {result_count} 个文本", 3000)
    
    def _on_ocr_error(self, error_msg: str):
        """OCR错误时的UI更新"""
        self.main_window.ocr_button.setEnabled(True)
        self.main_window.ocr_button.setText("🔍 开始OCR识别")
        self.main_window.progress_bar.setVisible(False)
        
        QMessageBox.critical(self.main_window, "OCR识别错误", error_msg)
        self.main_window.status_bar.showMessage("❌ OCR识别失败", 3000)
```

这个OCR控制器示例展示了如何将OCR相关的功能从MainWindow中提取出来，形成一个独立的、职责清晰的控制器模块。通过这种方式，可以显著减少MainWindow的复杂度，同时提高代码的可维护性和可测试性。

## 总结

我已经为您提供了完整的MainWindow拆分重构方案，包括：

### 📋 分析成果

1. **拆分重构方案** - 详细的模块划分和架构设计
2. **实施指南** - 具体的代码示例和分步骤实施
3. **OCR控制器示例** - 展示如何提取具体功能模块

### 🎯 拆分目标

- **主窗口**: 从3157行减少到~400行
- **功能模块**: 拆分为8个独立模块
- **单个文件**: 每个文件不超过500行
- **职责清晰**: 每个模块单一职责

### 🏗️ 拆分架构

```
ui/main_window.py (400行) - 主窗口协调器
├── components/ - UI组件模块
│   ├── ui_builder.py (300行)
│   ├── menu_toolbar.py (200行)
│   └── status_panel.py (150行)
├── controllers/ - 控制器模块  
│   ├── ocr_controller.py (500行)
│   ├── annotation_controller.py (600行)
│   ├── file_controller.py (400行)
│   └── event_controller.py (200行)
└── services/ - 服务模块
    ├── data_service.py (300行)
    ├── state_service.py (200行)
    └── config_service.py (150行)
```

### 🚀 实施建议

1. **分阶段实施**: 按优先级逐步重构
2. **保持兼容**: 通过委托方法保持向后兼容
3. **充分测试**: 每阶段完成后进行全面测试
4. **文档更新**: 及时更新架构文档

这个重构方案将显著提升代码的可维护性、可扩展性和团队协作效率，为项目的长期发展奠定坚实基础。
