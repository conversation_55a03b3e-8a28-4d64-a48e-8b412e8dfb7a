#!/usr/bin/env python3
"""
测试区域OCR坐标修复的脚本
"""

def test_coordinate_transformation():
    """测试简化的坐标变换公式"""

    print("=== 测试区域OCR坐标变换修复（简化版本）===\n")

    # 模拟一个竖排文本区域
    rect_width = 50   # 选择区域宽度
    rect_height = 150 # 选择区域高度（高 > 宽，判定为竖排）
    offset_x = 100    # 选择区域在原图中的x偏移
    offset_y = 200    # 选择区域在原图中的y偏移

    print(f"选择区域: 宽度={rect_width}, 高度={rect_height}")
    print(f"区域偏移: x={offset_x}, y={offset_y}")
    print(f"高宽比: {rect_height/rect_width:.2f} (>1.2, 判定为竖排文本)")
    print()

    # 模拟OCR识别结果（相对于裁剪区域的坐标）
    # 注意：OCR处理器内部已经处理了旋转，返回的是相对于裁剪区域的正确坐标
    ocr_bbox_relative = [
        [10, 5],    # 左上
        [40, 5],    # 右上
        [40, 25],   # 右下
        [10, 25]    # 左下
    ]

    ocr_center_x_relative = 25  # (10+40)/2
    ocr_center_y_relative = 15  # (5+25)/2

    print("OCR返回的相对坐标（相对于裁剪区域）:")
    print(f"  边界框: {ocr_bbox_relative}")
    print(f"  中心点: ({ocr_center_x_relative}, {ocr_center_y_relative})")
    print()

    # 应用简化的坐标变换公式
    print("应用简化的坐标变换（只加偏移量）:")

    # 变换边界框
    transformed_bbox = []
    for point in ocr_bbox_relative:
        # 简化的变换：只需要添加偏移量
        original_x = offset_x + point[0]
        original_y = offset_y + point[1]
        transformed_bbox.append([original_x, original_y])
        print(f"  点 {point} -> ({original_x}, {original_y})")

    # 变换中心点
    original_center_x = offset_x + ocr_center_x_relative
    original_center_y = offset_y + ocr_center_y_relative

    print(f"  中心点: ({ocr_center_x_relative}, {ocr_center_y_relative}) -> ({original_center_x}, {original_center_y})")
    print()

    # 验证结果
    print("验证结果:")
    print(f"  变换后边界框: {transformed_bbox}")
    print(f"  变换后中心点: ({original_center_x}, {original_center_y})")

    # 检查变换后的边界框是否在原始选择区域内
    min_x = min(point[0] for point in transformed_bbox)
    max_x = max(point[0] for point in transformed_bbox)
    min_y = min(point[1] for point in transformed_bbox)
    max_y = max(point[1] for point in transformed_bbox)

    expected_min_x = offset_x
    expected_max_x = offset_x + rect_width
    expected_min_y = offset_y
    expected_max_y = offset_y + rect_height

    print(f"  边界框范围: x=[{min_x}, {max_x}], y=[{min_y}, {max_y}]")
    print(f"  期望范围: x=[{expected_min_x}, {expected_max_x}], y=[{expected_min_y}, {expected_max_y}]")

    # 检查是否在合理范围内
    x_in_range = expected_min_x <= min_x <= max_x <= expected_max_x
    y_in_range = expected_min_y <= min_y <= max_y <= expected_max_y

    print(f"  X坐标范围正确: {'✅' if x_in_range else '❌'}")
    print(f"  Y坐标范围正确: {'✅' if y_in_range else '❌'}")

    if x_in_range and y_in_range:
        print("\n🎉 简化的坐标变换修复成功！")
    else:
        print("\n⚠️ 坐标变换仍有问题，需要进一步调试")

def test_bbox_dimensions():
    """测试边界框尺寸交换"""
    print("\n=== 测试边界框尺寸交换 ===\n")
    
    # 模拟旋转后的bbox尺寸
    bbox_width_rotated = 30   # 旋转后的宽度
    bbox_height_rotated = 20  # 旋转后的高度
    
    print(f"旋转后的边界框尺寸: 宽度={bbox_width_rotated}, 高度={bbox_height_rotated}")
    
    # 应用修复：竖排文本需要交换宽高
    original_width = bbox_height_rotated   # 20
    original_height = bbox_width_rotated   # 30
    
    print(f"修复后的边界框尺寸: 宽度={original_width}, 高度={original_height}")
    print(f"高宽比: {original_height/original_width:.2f} (应该>1，符合竖排文本特征)")
    
    if original_height > original_width:
        print("✅ 边界框尺寸交换正确")
    else:
        print("❌ 边界框尺寸交换有问题")

if __name__ == "__main__":
    test_coordinate_transformation()
    test_bbox_dimensions()
