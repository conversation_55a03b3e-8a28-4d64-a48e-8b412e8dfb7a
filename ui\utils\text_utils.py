#!/usr/bin/env python3
"""
文本处理和验证工具模块
"""

import re
from typing import Optional, Tuple, Dict, List

def clean_ocr_text(text: str) -> str:
    """清理OCR识别的文本"""
    if not text:
        return ""
    
    # 移除多余空白
    text = re.sub(r'\s+', ' ', text.strip())
    
    # 修正常见OCR错误
    corrections = {
        'O': '0',  # 字母O替换为数字0
        'l': '1',  # 小写l替换为数字1
        'I': '1',  # 大写I替换为数字1
    }
    
    # 只在数字上下文中进行替换
    if re.search(r'\d', text):
        for old, new in corrections.items():
            text = text.replace(old, new)
    
    return text

def format_dimension_text(text: str) -> str:
    """格式化尺寸文本"""
    text = clean_ocr_text(text)
    
    # 标准化直径符号
    text = re.sub(r'[Øø∅⌀]', 'Ø', text)
    
    # 标准化半径符号
    text = re.sub(r'[Rr](\d)', r'R\1', text)
    
    # 标准化单位
    text = re.sub(r'(\d+(?:\.\d+)?)\s*mm', r'\1', text)
    
    return text

def extract_numeric_value(text: str) -> Optional[float]:
    """从文本中提取数值"""
    text = clean_ocr_text(text)
    
    # 匹配数字（包括小数）
    match = re.search(r'(\d+(?:\.\d+)?)', text)
    if match:
        try:
            return float(match.group(1))
        except ValueError:
            pass
    
    return None

def validate_tolerance_format(text: str) -> bool:
    """验证公差格式是否正确"""
    text = clean_ocr_text(text)
    
    # 公差格式模式
    patterns = [
        r'^[+-]\d+(\.\d+)?$',  # ±数值
        r'^±\d+(\.\d+)?$',     # ±数值
        r'^[+-]0\.\d+$',       # ±0.xxx
        r'^\+\d+(\.\d+)?/-\d+(\.\d+)?$',  # +上公差/-下公差
    ]
    
    return any(re.match(pattern, text) for pattern in patterns)

def parse_tolerance_values(text: str) -> Tuple[Optional[str], Optional[str]]:
    """解析公差值，返回(上公差, 下公差)"""
    text = clean_ocr_text(text)
    
    # 处理±格式
    pm_match = re.match(r'^±(\d+(?:\.\d+)?)$', text)
    if pm_match:
        value = pm_match.group(1)
        return (f"+{value}", f"-{value}")
    
    # 处理+/-格式
    plus_minus_match = re.match(r'^\+(\d+(?:\.\d+)?)/\-(\d+(?:\.\d+)?)$', text)
    if plus_minus_match:
        upper = f"+{plus_minus_match.group(1)}"
        lower = f"-{plus_minus_match.group(2)}"
        return (upper, lower)
    
    # 处理单独的+或-
    if text.startswith('+'):
        plus_match = re.match(r'^\+(\d+(?:\.\d+)?)$', text)
        if plus_match:
            return (f"+{plus_match.group(1)}", None)
    elif text.startswith('-'):
        minus_match = re.match(r'^\-(\d+(?:\.\d+)?)$', text)
        if minus_match:
            return (None, f"-{minus_match.group(1)}")
    
    return (None, None)

def is_dimension_text(text: str) -> bool:
    """判断是否为尺寸文本"""
    text = clean_ocr_text(text)
    
    patterns = [
        r'^\d+(\.\d+)?$',      # 纯数字
        r'^\d+(\.\d+)?mm$',    # 带mm单位
        r'^Ø\d+(\.\d+)?$',     # 直径标注
        r'^R\d+(\.\d+)?$',     # 半径标注
        r'^\d+(\.\d+)?×\d+(\.\d+)?$',  # 长×宽格式
        r'^Φ\d+(\.\d+)?$',     # Φ直径标注
    ]
    
    return any(re.match(pattern, text) for pattern in patterns)

def is_thread_specification(text: str) -> bool:
    """判断是否为螺纹规格"""
    text = clean_ocr_text(text)
    
    patterns = [
        r'^M\d+$',                    # M8, M10等
        r'^M\d+[×x]\d+(\.\d+)?$',     # M8×1.25等
        r'^M\d+-\d+(\.\d+)?$',        # M8-1.25等
    ]
    
    return any(re.match(pattern, text) for pattern in patterns)

def standardize_thread_text(text: str) -> str:
    """标准化螺纹文本格式"""
    text = clean_ocr_text(text)
    
    # 统一使用×符号
    text = re.sub(r'[x×]', '×', text)
    
    # 确保M后面直接跟数字
    text = re.sub(r'M\s+(\d)', r'M\1', text)
    
    return text

def parse_annotation_text(text: str) -> Dict[str, str]:
    """
    解析标注文本，提取尺寸和公差信息
    返回包含dimension, dimension_type, upper_tolerance, lower_tolerance的字典
    """
    result = {
        'dimension': '',
        'dimension_type': '',
        'upper_tolerance': '',
        'lower_tolerance': ''
    }

    if not text:
        return result

    original_text = text
    remaining_text = ""

    # 如果文本是用换行符分隔的
    if '\n' in text:
        # 拆分和预处理
        parts = [p.strip().replace(' ', '').replace('O', '0').replace('o', '0').replace('Ø', 'Φ').replace('∅', 'Φ') for p in original_text.split('\n')]
        parts = [p for p in parts if p]

        if not parts:
            return result

        # 识别和分配组件
        dimension_part = parts[0]
        tolerance_parts = parts[1:]

        # 解析公差
        is_pm_tolerance = False
        for part in tolerance_parts:
            pm_match = re.match(r'^[±](\d+\.?\d*)$', part)
            if pm_match:
                tolerance_value = pm_match.group(1)
                result['upper_tolerance'] = f"+{tolerance_value}"
                result['lower_tolerance'] = f"-{tolerance_value}"
                is_pm_tolerance = True
                break

        if not is_pm_tolerance:
            for part in tolerance_parts:
                if part.startswith('+'):
                    plus_match = re.match(r'^\+(\d+\.?\d*)$', part)
                    if plus_match:
                        result['upper_tolerance'] = f"+{plus_match.group(1)}"
                elif part.startswith('-'):
                    minus_match = re.match(r'^\-(\d+\.?\d*)$', part)
                    if minus_match:
                        result['lower_tolerance'] = f"-{minus_match.group(1)}"
                elif part == '0':
                    if not result['upper_tolerance']:
                        result['upper_tolerance'] = '0'
                    else:
                        result['lower_tolerance'] = '0'
        
        remaining_text = dimension_part

    else:
        # 预处理：规范化符号
        processed_text = text.replace(' ', '').replace('O', '0').replace('o', '0')
        processed_text = processed_text.replace('Ø', 'Φ').replace('∅', 'Φ')
        remaining_text = processed_text

        # 优先提取公差
        pm_match = re.search(r'±(\d+\.?\d*)', remaining_text)
        if pm_match:
            tolerance_value = pm_match.group(1)
            result['upper_tolerance'] = f"+{tolerance_value}"
            result['lower_tolerance'] = f"-{tolerance_value}"
            remaining_text = remaining_text.replace(pm_match.group(0), '', 1)
        else:
            # 分别提取 + 和 - 公差
            plus_match = re.search(r'\+(\d+\.?\d*)', remaining_text)
            if plus_match:
                result['upper_tolerance'] = f"+{plus_match.group(1)}"
                remaining_text = remaining_text.replace(plus_match.group(0), '', 1)

            minus_match = re.search(r'\-(\d+\.?\d*)', remaining_text)
            if minus_match:
                result['lower_tolerance'] = f"-{minus_match.group(1)}"
                remaining_text = remaining_text.replace(minus_match.group(0), '', 1)

    # 解析尺寸和类型
    if remaining_text:
        # 检测尺寸类型
        if remaining_text.startswith('Φ'):
            result['dimension_type'] = 'diameter'
            result['dimension'] = remaining_text[1:]  # 移除Φ符号
        elif remaining_text.startswith('R'):
            result['dimension_type'] = 'radius'
            result['dimension'] = remaining_text[1:]  # 移除R符号
        elif 'M' in remaining_text and ('×' in remaining_text or 'x' in remaining_text):
            result['dimension_type'] = 'thread'
            result['dimension'] = remaining_text
        else:
            result['dimension_type'] = 'linear'
            result['dimension'] = remaining_text

    return result

def is_tolerance_text(text: str) -> bool:
    """判断是否为公差文本"""
    text = clean_ocr_text(text)
    
    patterns = [
        r'^[+-]\d+(\.\d+)?$',  # ±数值
        r'^±\d+(\.\d+)?$',     # ±数值
        r'^[+-]0\.\d+$',       # ±0.xxx
        r'^0$',                # 单独的0
    ]
    
    return any(re.match(pattern, text) for pattern in patterns)

def normalize_symbols(text: str) -> str:
    """标准化文本中的符号"""
    if not text:
        return ""
    
    # 标准化直径符号
    text = text.replace('Ø', 'Φ').replace('∅', 'Φ').replace('⌀', 'Φ')
    
    # 标准化乘号
    text = text.replace('×', 'x').replace('X', 'x')
    
    # 标准化公差符号
    text = text.replace('±', '±')
    
    return text

def extract_dimension_value(text: str) -> Optional[float]:
    """从尺寸文本中提取数值"""
    text = clean_ocr_text(text)
    
    # 移除符号前缀
    text = re.sub(r'^[ΦØ∅⌀Rr]', '', text)
    
    # 提取数值
    return extract_numeric_value(text)

def format_tolerance_display(upper: str, lower: str) -> str:
    """格式化公差显示"""
    if not upper and not lower:
        return ""
    
    if upper and lower:
        # 检查是否为对称公差
        if upper.startswith('+') and lower.startswith('-'):
            upper_val = upper[1:]
            lower_val = lower[1:]
            if upper_val == lower_val:
                return f"±{upper_val}"
        return f"{upper}/{lower}"
    
    return upper or lower
