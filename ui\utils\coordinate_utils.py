#!/usr/bin/env python3
"""
坐标转换和几何计算工具模块
"""

import math
from typing import List, Tuple, Optional
from PySide6.QtCore import QPointF, QRectF
from PySide6.QtGui import QTransform

def map_scene_to_view_coordinates(scene_point: QPointF, transform: QTransform) -> QPointF:
    """将场景坐标转换为视图坐标"""
    if transform.isInvertible():
        inverted_transform, _ = transform.inverted()
        return inverted_transform.map(scene_point)
    return scene_point

def calculate_distance_between_points(p1: QPointF, p2: QPointF) -> float:
    """计算两点之间的距离"""
    dx = p2.x() - p1.x()
    dy = p2.y() - p1.y()
    return math.sqrt(dx * dx + dy * dy)

def get_rect_center(rect: QRectF) -> QPointF:
    """获取矩形的中心点"""
    return QPointF(
        rect.x() + rect.width() / 2,
        rect.y() + rect.height() / 2
    )

def normalize_rect(rect: QRectF) -> QRectF:
    """标准化矩形（确保宽高为正值）"""
    return rect.normalized()

def calculate_rect_area(rect: QRectF) -> float:
    """计算矩形面积"""
    return rect.width() * rect.height()

def is_point_in_rect(point: QPointF, rect: QRectF) -> bool:
    """判断点是否在矩形内"""
    return rect.contains(point)

def expand_rect(rect: QRectF, margin: float) -> QRectF:
    """扩展矩形边界"""
    return QRectF(
        rect.x() - margin,
        rect.y() - margin,
        rect.width() + 2 * margin,
        rect.height() + 2 * margin
    )

def calculate_angle_between_points(p1: QPointF, p2: QPointF) -> float:
    """计算两点之间的角度（弧度）"""
    dx = p2.x() - p1.x()
    dy = p2.y() - p1.y()
    return math.atan2(dy, dx)

def rotate_point_around_center(point: QPointF, center: QPointF, angle: float) -> QPointF:
    """围绕中心点旋转点"""
    cos_a = math.cos(angle)
    sin_a = math.sin(angle)
    
    # 平移到原点
    dx = point.x() - center.x()
    dy = point.y() - center.y()
    
    # 旋转
    new_x = dx * cos_a - dy * sin_a
    new_y = dx * sin_a + dy * cos_a
    
    # 平移回去
    return QPointF(new_x + center.x(), new_y + center.y())

def calculate_bbox_center(bbox_points: List[QPointF]) -> QPointF:
    """计算边界框的中心点"""
    if not bbox_points:
        return QPointF(0, 0)
    
    center_x = sum(p.x() for p in bbox_points) / len(bbox_points)
    center_y = sum(p.y() for p in bbox_points) / len(bbox_points)
    return QPointF(center_x, center_y)

def calculate_bbox_from_points(bbox_points: List[QPointF]) -> QRectF:
    """从点列表计算边界框矩形"""
    if not bbox_points:
        return QRectF()
    
    x_coords = [p.x() for p in bbox_points]
    y_coords = [p.y() for p in bbox_points]
    
    min_x, max_x = min(x_coords), max(x_coords)
    min_y, max_y = min(y_coords), max(y_coords)
    
    return QRectF(min_x, min_y, max_x - min_x, max_y - min_y)

def create_rect_from_center_and_size(center: QPointF, width: float, height: float) -> QRectF:
    """从中心点和尺寸创建矩形"""
    return QRectF(
        center.x() - width / 2,
        center.y() - height / 2,
        width,
        height
    )

def clamp_rect_to_bounds(rect: QRectF, bounds: QRectF) -> QRectF:
    """将矩形限制在边界内"""
    x = max(bounds.x(), min(rect.x(), bounds.right() - rect.width()))
    y = max(bounds.y(), min(rect.y(), bounds.bottom() - rect.height()))
    
    width = min(rect.width(), bounds.width())
    height = min(rect.height(), bounds.height())
    
    return QRectF(x, y, width, height)

def scale_rect(rect: QRectF, scale_factor: float) -> QRectF:
    """按比例缩放矩形"""
    center = get_rect_center(rect)
    new_width = rect.width() * scale_factor
    new_height = rect.height() * scale_factor
    
    return create_rect_from_center_and_size(center, new_width, new_height)

def rect_intersects_any(rect: QRectF, rect_list: List[QRectF]) -> bool:
    """检查矩形是否与列表中任何矩形相交"""
    return any(rect.intersects(other_rect) for other_rect in rect_list)

def get_rect_corners(rect: QRectF) -> List[QPointF]:
    """获取矩形的四个角点"""
    return [
        QPointF(rect.left(), rect.top()),      # 左上
        QPointF(rect.right(), rect.top()),     # 右上
        QPointF(rect.right(), rect.bottom()),  # 右下
        QPointF(rect.left(), rect.bottom())    # 左下
    ]

def create_preview_rect_with_padding(bbox_points: List[QPointF], padding_ratio: float = 0.2) -> QRectF:
    """创建带填充的预览矩形"""
    if not bbox_points:
        return QRectF()
    
    bbox_rect = calculate_bbox_from_points(bbox_points)
    padding = max(bbox_rect.width(), bbox_rect.height()) * padding_ratio
    
    return expand_rect(bbox_rect, padding)

def convert_bbox_to_qpointf_list(bbox: List[List[float]]) -> List[QPointF]:
    """将bbox坐标列表转换为QPointF列表"""
    return [QPointF(point[0], point[1]) for point in bbox]

def convert_qpointf_list_to_bbox(points: List[QPointF]) -> List[List[float]]:
    """将QPointF列表转换为bbox坐标列表"""
    return [[p.x(), p.y()] for p in points]

def is_rect_valid(rect: QRectF, min_size: float = 1.0) -> bool:
    """检查矩形是否有效（非空且大小合理）"""
    return (rect.width() >= min_size and 
            rect.height() >= min_size and 
            not rect.isEmpty())

def calculate_rect_aspect_ratio(rect: QRectF) -> float:
    """计算矩形的宽高比"""
    if rect.height() == 0:
        return float('inf')
    return rect.width() / rect.height()

def is_vertical_rect(rect: QRectF, threshold: float = 1.2) -> bool:
    """判断矩形是否为竖直方向（高>宽）"""
    if rect.width() == 0:
        return True
    return (rect.height() / rect.width()) > threshold
