#!/usr/bin/env python3
"""
文件管理模块
负责处理文件加载、PDF页面管理、文件缓存等功能
"""

import time
import threading
from pathlib import Path
from PySide6.QtWidgets import QFileDialog, QMessageBox, QApplication, QInputDialog
from PySide6.QtCore import QPointF, Qt
from PySide6.QtGui import QPixmap, QColor

from ui.workers.file_loader_worker import FileLoaderWorker
from ui.workers.pdf_loader_worker import PDFLoaderWorker, LoadPDFEvent
from core.annotation_item import BubbleAnnotationItem
from utils.constants import (
    FILE_DIALOG_FILTER, SUPPORTED_IMAGE_FORMATS, SUPPORTED_PDF_FORMATS, 
    SUPPORTED_DXF_FORMATS, PDF_QUALITY_OPTIONS
)

import logging
logger = logging.getLogger('PyQtBubble')

class FileManager:
    """文件管理器类"""
    
    def __init__(self, main_window):
        """
        初始化文件管理器
        
        Args:
            main_window: 主窗口实例，用于访问UI组件和数据
        """
        self.main_window = main_window
    
    def open_file(self):
        """打开文件对话框选择文件"""
        # 创建文件对话框并设置过滤器
        file_dialog = QFileDialog(self.main_window)
        file_dialog.setWindowTitle("打开文件")
        file_dialog.setFileMode(QFileDialog.ExistingFile)
        file_dialog.setNameFilter(FILE_DIALOG_FILTER)
        
        # 设置默认目录
        if self.main_window.current_file_path:
            dir_path = str(Path(self.main_window.current_file_path).parent)
            file_dialog.setDirectory(dir_path)
        
        # 显示对话框
        if file_dialog.exec():
            file_paths = file_dialog.selectedFiles()
            if file_paths:
                self.main_window.status_bar.showMessage(f"正在加载文件: {file_paths[0]}...")
                self.load_file(file_paths[0])
    
    def load_file(self, file_path: str):
        """加载文件

        Args:
            file_path: 文件路径
        """
        if not file_path or not Path(file_path).exists():
            QMessageBox.warning(self.main_window, "错误", f"文件不存在: {file_path}")
            self.main_window.status_bar.clearMessage()
            return
            
        # 清除当前选中的标注
        if self.main_window.current_annotation:
            self.main_window.current_annotation = None
            self.main_window.property_editor.set_annotation(None, None, None)
        
        # 转换为Path对象以便于处理
        file_path = Path(file_path)
        extension = file_path.suffix.lower()
        
        # 确保扩展名有效
        if extension not in SUPPORTED_IMAGE_FORMATS + SUPPORTED_PDF_FORMATS + SUPPORTED_DXF_FORMATS:
            QMessageBox.warning(self.main_window, "错误", f"不支持的文件格式: {extension}")
            self.main_window.status_bar.clearMessage()
            return
        
        # 显示加载对话框
        self.main_window.loading_label.setText(f"⏳ 正在加载文件...\n{file_path.name}")
        self.main_window.loading_dialog.resize(self.main_window.size())
        self.main_window.loading_dialog.show()
        QApplication.processEvents()  # 确保UI立即更新
        
        # 清除当前状态
        self.main_window.clear_annotations(show_empty_message=False)
        self.main_window.annotation_table.clear_annotations()  # 确保表格也被清空
        self.main_window.clear_ocr_results()
        self.main_window.graphics_scene.clear()
        self.main_window.annotation_counter = 0  # 重置标注计数器
        
        # 重置PDF相关状态
        self.main_window.pdf_file_path = None
        self.main_window.pdf_page_count = 0
        self.main_window.current_pdf_page = 0
        self.main_window.pdf_pages_cache.clear()
        self.main_window.annotations_by_page.clear()
        self.main_window.ocr_results_by_page.clear()
        
        # 隐藏PDF导航控件
        self.main_window.pdf_nav_widget.setVisible(False)
        
        # 创建并启动文件加载线程
        file_loader = FileLoaderWorker(
            str(file_path),
            pdf_quality=self.main_window.pdf_quality_combo.currentText()
        )
        
        # 连接信号到MainWindow的方法
        file_loader.signals.progress.connect(self.main_window._on_file_load_progress)
        file_loader.signals.finished.connect(self.main_window._on_file_loaded)
        file_loader.signals.pdf_loaded.connect(self.main_window._on_pdf_file_loaded)
        file_loader.signals.error.connect(self.main_window._on_file_load_error)
        
        # 启动线程
        self.main_window.thread_pool.start(file_loader)

    def _on_file_load_progress(self, progress: int, message: str):
        """文件加载进度更新"""
        self.main_window.loading_label.setText(f"⏳ {message}\n({progress}%)")
        self.main_window.status_bar.showMessage(message)
        QApplication.processEvents()

    def _on_file_loaded(self, file_path: str, pixmap: QPixmap):
        """文件加载完成回调

        Args:
            file_path: 文件路径
            pixmap: 图像数据
        """
        try:
            self.main_window.current_file_path = file_path
            self.main_window.current_pixmap = pixmap

            # 清除当前场景
            self.main_window.graphics_scene.clear()

            # 添加图像到场景
            self.main_window.graphics_scene.addPixmap(pixmap)
            logger.debug(f"图像已添加到场景，尺寸: {pixmap.width()}x{pixmap.height()}")

            # 初始化标注ID计数器
            self.main_window.annotation_counter = 0

            # 执行一次居中操作，之后不干扰用户缩放
            self.center_view()

            # 更新状态栏
            file_name = Path(file_path).name
            self.main_window.status_bar.showMessage(f"已加载: {file_name} ({pixmap.width()}x{pixmap.height()})", 5000)

            # 隐藏加载对话框
            self.main_window.loading_dialog.hide()
        except Exception as e:
            self._on_file_load_error(f"处理加载结果时出错: {str(e)}")
            logger.exception("图像加载完成处理错误")

    def _on_pdf_file_loaded(self, file_path: str, page_count: int):
        """PDF文件加载完成"""
        try:
            # 设置PDF相关属性
            self.main_window.pdf_file_path = file_path
            self.main_window.pdf_page_count = page_count
            self.main_window.current_pdf_page = 0  # 从第一页开始

            # 如果是多页PDF，显示导航控件
            if page_count > 1:
                self.update_pdf_navigation_controls()

                # 在状态栏显示提示消息，而不是弹窗
                self.main_window.status_bar.showMessage(f"多页PDF文件，共 {page_count} 页，使用右下角导航控件或键盘方向键切换页面", 5000)

            # 加载第一页
            self.load_pdf_page(0)

        except Exception as e:
            self._on_file_load_error(f"处理PDF文件时出错: {str(e)}")

    def _on_file_load_error(self, error_msg: str):
        """文件加载错误处理"""
        QMessageBox.warning(self.main_window, "加载错误", error_msg)
        self.main_window.status_bar.showMessage(f"❌ 文件加载失败: {error_msg}", 5000)
        self.main_window.loading_dialog.hide()

    def center_view(self):
        """确保图像在视图中居中显示"""
        if not self.main_window.graphics_scene.items():
            return

        logger.debug("开始执行center_view()方法")

        # 直接调用一次centerContent，不使用任何定时器或延迟
        self.main_window.graphics_view.centerContent()

        # 强制处理所有待处理事件，确保界面更新
        QApplication.processEvents()

        logger.debug("center_view()方法执行完成")

    def load_pdf_page(self, page_index: int, skip_save: bool = False):
        """加载指定页码的PDF页面

        Args:
            page_index: 页码（从0开始）
            skip_save: 是否跳过保存当前页数据的步骤。用于在执行了外部数据修改（如全局重排）后刷新视图。

        Returns:
            bool: 是否成功启动加载过程
        """
        if not self.main_window.pdf_file_path or page_index not in range(self.main_window.pdf_page_count):
            return False

        start_time = time.time()
        logger.debug(f"开始加载PDF页面 {page_index+1}/{self.main_window.pdf_page_count}")

        # 设置加载对话框文本并显示
        self.main_window.loading_label.setText(f"⏳ 正在加载第 {page_index+1}/{self.main_window.pdf_page_count} 页...\n请稍候")
        self.main_window.loading_dialog.resize(self.main_window.size())
        # 非阻塞方式显示
        self.main_window.loading_dialog.show()
        QApplication.processEvents()  # 确保UI立即更新
        logger.debug(f"显示加载对话框耗时: {time.time() - start_time:.2f}秒")

        # 记录之前的页码，以便加载失败时可以恢复
        self.main_window.previous_page = self.main_window.current_pdf_page

        # 清除当前选择状态，防止引用已删除的对象
        self.main_window.graphics_scene.clearSelection()
        self.main_window.current_annotation = None
        self.main_window.property_editor.set_annotation(None, None, None)
        logger.debug(f"清除当前选择状态耗时: {time.time() - start_time:.2f}秒")

        if not skip_save:
            # 保存当前页面的标注和OCR结果
            if self.main_window.current_pdf_page in range(self.main_window.pdf_page_count):
                save_start = time.time()
                self.save_current_page_data()
                logger.debug(f"保存当前页面数据耗时: {time.time() - save_start:.2f}秒")
        else:
            logger.debug("跳过页面数据保存（按需刷新模式）")

        # 更新当前页码
        self.main_window.current_pdf_page = page_index

        # 更新导航按钮状态
        self.update_pdf_navigation_controls()

        # 检查是否已经缓存了该页面
        if page_index in self.main_window.pdf_pages_cache:
            temp_path = self.main_window.pdf_pages_cache[page_index]
            # 检查临时文件是否仍然存在
            if Path(temp_path).exists():
                logger.debug(f"发现页面缓存: {temp_path}")
                self.main_window.status_bar.showMessage(f"从缓存加载PDF页面 {page_index+1}/{self.main_window.pdf_page_count}...")
                self.main_window.loading_label.setText(f"正在从缓存加载第 {page_index+1}/{self.main_window.pdf_page_count} 页...\n请稍候")
                QApplication.processEvents()  # 确保UI立即更新

                # 在后台线程中加载缓存图像
                def load_cached_image():
                    try:
                        cache_start = time.time()
                        logger.debug(f"开始从缓存加载图像...")
                        pixmap = QPixmap(temp_path)
                        if not pixmap.isNull():
                            logger.debug(f"缓存图像加载成功，耗时: {time.time() - cache_start:.2f}秒")
                            # 在主线程中更新UI
                            QApplication.instance().postEvent(self.main_window, LoadPDFEvent(pixmap, temp_path))
                            return True
                        else:
                            logger.error(f"缓存图像加载失败: pixmap为空")
                    except Exception as e:
                        logger.exception(f"加载缓存图像出错: {e}")
                        return False

                # 创建线程并启动
                thread = threading.Thread(target=load_cached_image)
                thread.daemon = True
                logger.debug(f"启动缓存图像加载线程")
                thread.start()
                return True

        # 缓存中没有或临时文件已被删除，重新转换
        zoom_factor = PDF_QUALITY_OPTIONS.get(self.main_window.pdf_quality_combo.currentText(), 4.0)
        logger.debug(f"未找到缓存，开始转换PDF，缩放因子: {zoom_factor}")
        self.main_window.status_bar.showMessage(f"正在转换PDF页面 {page_index+1}/{self.main_window.pdf_page_count}...")

        # 更新加载提示
        self.main_window.loading_label.setText(f"正在转换第 {page_index+1}/{self.main_window.pdf_page_count} 页...\n请稍候")
        QApplication.processEvents()  # 确保UI立即更新

        # 清除当前场景
        self.main_window.graphics_scene.clear()

        # 创建PDF加载工作线程
        pdf_loader = PDFLoaderWorker(
            self.main_window.pdf_file_path,
            page_index,
            quality=zoom_factor,
            force_resolution=self.main_window.force_resolution_checkbox.isChecked()
        )

        # 连接信号到MainWindow的方法
        pdf_loader.signals.finished.connect(self.main_window._on_pdf_loaded)
        pdf_loader.signals.error.connect(self.main_window._on_pdf_load_error)

        # 启动线程
        logger.debug(f"启动PDF加载线程，总准备耗时: {time.time() - start_time:.2f}秒")
        self.main_window.thread_pool.start(pdf_loader)

        return True

    def _on_pdf_loaded(self, pixmap: QPixmap, temp_path: str):
        """PDF加载完成处理"""
        try:
            start_time = time.time()
            logger.debug(f"PDF加载完成回调开始处理")

            # 缓存此页面
            self.main_window.pdf_pages_cache[self.main_window.current_pdf_page] = temp_path
            self.main_window.current_pixmap = pixmap
            self.main_window.current_file_path = temp_path

            # 清除并设置场景
            self.main_window.graphics_scene.clear()
            self.main_window.graphics_scene.addPixmap(pixmap)
            logger.debug(f"更新场景耗时: {time.time() - start_time:.2f}秒")

            # 强制处理事件，确保场景已更新
            QApplication.processEvents()

            # 仅在初始加载时执行一次居中操作
            self.center_view()

            # 恢复此页面的数据
            restore_start = time.time()
            self.restore_page_data(self.main_window.current_pdf_page)
            logger.debug(f"恢复页面数据耗时: {time.time() - restore_start:.2f}秒")

            self.main_window.status_bar.showMessage(f"✅ 页面加载成功: {self.main_window.current_pdf_page+1}/{self.main_window.pdf_page_count}", 3000)

            # 隐藏加载对话框
            self.main_window.loading_dialog.hide()
            logger.debug(f"PDF加载完成处理总耗时: {time.time() - start_time:.2f}秒")
        except Exception as e:
            logger.exception(f"PDF加载完成处理中发生异常: {str(e)}")
            self._on_pdf_load_error(f"处理加载结果时出错: {str(e)}")

    def _on_pdf_load_error(self, error_msg: str):
        """PDF加载出错处理"""
        logger.error(f"PDF加载错误: {error_msg}")
        # 恢复到之前的页面
        self.main_window.current_pdf_page = self.main_window.previous_page
        self.update_pdf_navigation_controls()

        # 如果当前场景是空的，尝试恢复之前的页面内容
        if len(self.main_window.graphics_scene.items()) == 0 and self.main_window.previous_page in self.main_window.pdf_pages_cache:
            try:
                logger.debug(f"尝试恢复到之前的页面: {self.main_window.previous_page+1}")
                prev_temp_path = self.main_window.pdf_pages_cache[self.main_window.previous_page]
                if Path(prev_temp_path).exists():
                    try:
                        prev_pixmap = QPixmap(prev_temp_path)
                        self.main_window.graphics_scene.addPixmap(prev_pixmap)
                        self.main_window.current_pixmap = prev_pixmap
                        self.main_window.current_file_path = prev_temp_path

                        # 直接居中显示，不使用延迟
                        self.main_window.graphics_view.centerContent()

                        self.restore_page_data(self.main_window.previous_page)
                        logger.debug(f"成功恢复到之前的页面")
                    except Exception as e:
                        logger.exception(f"恢复之前页面时出错: {str(e)}")
                        pass  # 如果恢复失败，至少保持当前状态
            except Exception as e:
                logger.exception(f"尝试恢复之前页面时出错: {str(e)}")

        QMessageBox.warning(self.main_window, "错误", f"加载PDF页面失败: {error_msg}")
        self.main_window.status_bar.showMessage(f"❌ 页面加载失败: {error_msg}", 3000)

        # 隐藏加载对话框
        self.main_window.loading_dialog.hide()

    def update_pdf_navigation_controls(self):
        """更新PDF导航控件的状态"""
        if not self.main_window.pdf_file_path:
            self.main_window.pdf_nav_widget.setVisible(False)
            return

        self.main_window.pdf_nav_widget.setVisible(self.main_window.pdf_page_count > 1)

        # 更新页码显示
        self.main_window.page_label.setText(f"{self.main_window.current_pdf_page+1}/{self.main_window.pdf_page_count}")

        # 更新导航按钮状态
        self.main_window.prev_page_btn.setEnabled(self.main_window.current_pdf_page > 0)
        self.main_window.next_page_btn.setEnabled(self.main_window.current_pdf_page < self.main_window.pdf_page_count - 1)
        self.main_window.go_to_page_btn.setEnabled(self.main_window.pdf_page_count > 1)

    def go_to_prev_page(self):
        """转到上一页"""
        if self.main_window.pdf_file_path and self.main_window.current_pdf_page > 0:
            self.load_pdf_page(self.main_window.current_pdf_page - 1)

    def go_to_next_page(self):
        """转到下一页"""
        if self.main_window.pdf_file_path and self.main_window.current_pdf_page < self.main_window.pdf_page_count - 1:
            self.load_pdf_page(self.main_window.current_pdf_page + 1)

    def show_go_to_page_dialog(self):
        """显示页面跳转对话框"""
        if not self.main_window.pdf_file_path or self.main_window.pdf_page_count <= 1:
            return

        page, ok = QInputDialog.getInt(
            self.main_window,
            "跳转到页面",
            f"请输入要跳转的页码 (1-{self.main_window.pdf_page_count}):",
            self.main_window.current_pdf_page + 1,  # 当前页码（从1开始）
            1, self.main_window.pdf_page_count, 1
        )

        if ok:
            self.load_pdf_page(page - 1)  # 转换为从0开始的索引

    def save_current_page_data(self):
        """保存当前页面的标注和OCR结果"""
        if self.main_window.current_pdf_page not in range(self.main_window.pdf_page_count):
            return

        # 保存当前页的OCR结果
        try:
            self.main_window.ocr_results_by_page[self.main_window.current_pdf_page] = self.main_window.ocr_results.copy()
        except Exception as e:
            print(f"保存OCR结果时出错: {e}")
            # 确保有一个空列表
            self.main_window.ocr_results_by_page[self.main_window.current_pdf_page] = []

        # 保存当前页的标注数据（不是对象引用）
        annotation_data_list = []

        # 安全地获取标注数据
        for annotation in list(self.main_window.annotations):  # 使用列表副本进行迭代
            try:
                # 检查对象是否有效
                if not annotation.scene():
                    print(f"警告: 标注 #{getattr(annotation, 'annotation_id', 'unknown')} 不在场景中，跳过保存")
                    continue

                # 提取标注的基本属性
                annotation_data = {
                    'annotation_id': annotation.annotation_id,
                    'text': annotation.text,
                    'style': annotation.style,
                    'shape_type': annotation.shape_type,
                    'radius': annotation.radius,
                    'base_radius': annotation.base_radius,
                    'scale_factor': annotation.scale_factor,
                    'dimension': annotation.dimension,
                    'dimension_type': annotation.dimension_type,
                    'upper_tolerance': annotation.upper_tolerance,
                    'lower_tolerance': annotation.lower_tolerance,
                    'is_audited': annotation.is_audited,
                    'auto_radius': annotation.auto_radius,
                    'pos_x': annotation.pos().x(),
                    'pos_y': annotation.pos().y(),
                    'anchor_x': annotation.anchor_point.x(),
                    'anchor_y': annotation.anchor_point.y(),
                }

                # 保存颜色信息
                if annotation.custom_color and annotation.custom_color.isValid():
                    annotation_data['color'] = {
                        'r': annotation.custom_color.red(),
                        'g': annotation.custom_color.green(),
                        'b': annotation.custom_color.blue(),
                        'a': annotation.custom_color.alpha(),
                    }
                else:
                    annotation_data['color'] = None

                # 保存边界框点信息
                if hasattr(annotation, 'bbox_points') and annotation.bbox_points:
                    bbox_points_data = []
                    for point in annotation.bbox_points:
                        bbox_points_data.append((point.x(), point.y()))
                    annotation_data['bbox_points'] = bbox_points_data
                else:
                    annotation_data['bbox_points'] = []

                annotation_data_list.append(annotation_data)
            except Exception as e:
                print(f"保存标注数据时出错: {e}")
                # 继续处理下一个标注

        # 存储数据字典而不是对象引用
        self.main_window.annotations_by_page[self.main_window.current_pdf_page] = annotation_data_list

    def restore_page_data(self, page_index: int):
        """恢复指定页面的标注和OCR结果"""
        # 先清空标注表，避免引用已删除对象
        self.main_window.annotation_table.clear_annotations()

        # 清除当前选中状态
        self.main_window.current_annotation = None
        self.main_window.property_editor.set_annotation(None, None, None)

        # 清理场景中的所有标注对象和OCR边界框
        items_to_remove = []
        for item in self.main_window.graphics_scene.items():
            # 删除标注对象和OCR边界框
            if isinstance(item, BubbleAnnotationItem) or \
               (item.data(Qt.UserRole) is not None and isinstance(item.data(Qt.UserRole), int) and item.data(Qt.UserRole) >= 10000):
                items_to_remove.append(item)

        # 从场景中移除所有标注和OCR边界框
        for item in items_to_remove:
            try:
                self.main_window.graphics_scene.removeItem(item)
            except Exception as e:
                print(f"移除项目时出错: {e}")
                # 继续处理

        # 清空当前标注列表和OCR结果
        self.main_window.annotations = []
        self.main_window.ocr_results = []

        # 恢复标注
        if page_index in self.main_window.annotations_by_page and self.main_window.annotations_by_page[page_index]:
            annotation_data_list = self.main_window.annotations_by_page[page_index]

            # 根据保存的数据重新创建标注对象
            for annotation_data in annotation_data_list:
                try:
                    # 创建位置
                    position = QPointF(annotation_data['pos_x'], annotation_data['pos_y'])
                    anchor_point = QPointF(annotation_data['anchor_x'], annotation_data['anchor_y'])

                    # 创建颜色对象
                    color = None
                    if annotation_data['color']:
                        color_data = annotation_data['color']
                        color = QColor(
                            color_data['r'],
                            color_data['g'],
                            color_data['b'],
                            color_data['a']
                        )

                    # 创建新的标注对象
                    annotation = BubbleAnnotationItem(
                        annotation_id=annotation_data['annotation_id'],
                        anchor_point=anchor_point,
                        text=annotation_data['text'],
                        style=annotation_data['style'],
                        shape=annotation_data['shape_type'],
                        color=color,
                        size=annotation_data['radius'],
                        dimension=annotation_data['dimension'],
                        dimension_type=annotation_data['dimension_type'],
                        upper_tolerance=annotation_data['upper_tolerance'],
                        lower_tolerance=annotation_data['lower_tolerance'],
                        is_audited=annotation_data['is_audited']
                    )

                    # 设置其他属性
                    annotation.setPos(position)
                    annotation.base_radius = annotation_data['base_radius']
                    annotation.scale_factor = annotation_data['scale_factor']
                    annotation.auto_radius = annotation_data['auto_radius']

                    # 恢复边界框点
                    if annotation_data['bbox_points']:
                        bbox_points = []
                        for point_tuple in annotation_data['bbox_points']:
                            bbox_points.append(QPointF(point_tuple[0], point_tuple[1]))
                        annotation.set_bbox_points(bbox_points)

                    # 连接信号
                    self._connect_annotation_signals(annotation)

                    # 添加到场景和列表
                    self.main_window.graphics_scene.addItem(annotation)
                    self.main_window.annotations.append(annotation)
                except Exception as e:
                    print(f"恢复标注时出错: {e}")
                    # 继续处理下一个标注

            # 更新标注计数器
            if self.main_window.annotations:
                self.main_window.annotation_counter = max(annotation.annotation_id for annotation in self.main_window.annotations)

        # 恢复OCR结果
        if page_index in self.main_window.ocr_results_by_page and self.main_window.ocr_results_by_page[page_index]:
            self.main_window.ocr_results = self.main_window.ocr_results_by_page[page_index].copy()

            # 重新显示OCR边界框
            self.main_window.display_ocr_results()
            self.main_window.update_ocr_stats()

        # 最后再刷新标注列表，确保使用的是当前页面的标注
        self.main_window.refresh_annotation_list()

    def _connect_annotation_signals(self, annotation):
        """连接标注对象的所有信号"""
        annotation.selected.connect(self.main_window.on_annotation_selected)
        annotation.moved.connect(self.main_window.on_annotation_moved)
        annotation.delete_requested.connect(self.main_window.delete_annotation)
        annotation.size_change_requested.connect(self.main_window.on_annotation_size_changed)
        annotation.shape_change_requested.connect(self.main_window.on_annotation_shape_changed)
        annotation.style_change_requested.connect(self.main_window.on_annotation_style_changed)
        annotation.color_change_requested.connect(self.main_window.on_annotation_color_changed)
        annotation.data_updated.connect(lambda: self.main_window.refresh_annotation_list())
