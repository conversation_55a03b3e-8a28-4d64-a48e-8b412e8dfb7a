#!/usr/bin/env python3
"""
UI相关常量配置
"""

from PySide6.QtCore import QSize

# 窗口配置
WINDOW_MIN_SIZE = (1200, 800)
WINDOW_DEFAULT_SIZE = (1600, 900)
WINDOW_TITLE = "图纸标注系统"

# 面板大小配置
PANEL_SIZES = {
    'ocr_panel_max_height': 200,
    'annotation_panel_min_height': 200,
    'property_panel_min_width': 300,
    'graphics_panel_min_size': (800, 600),
    'loading_dialog_size': (300, 100)
}

# 按钮样式配置
BUTTON_STYLES = {
    'primary': """
        QPushButton { 
            background-color: #007bff; 
            color: white; 
            font-weight: bold; 
            border: none; 
            min-height: 25px; 
            border-radius: 4px;
        } 
        QPushButton:hover { 
            background-color: #0056b3; 
        }
        QPushButton:disabled { 
            background-color: #cccccc; 
            color: #666666; 
        }
    """,
    'secondary': """
        QPushButton { 
            background-color: #6c757d; 
            color: white; 
            border: none; 
            min-height: 25px; 
            border-radius: 4px;
        } 
        QPushButton:hover { 
            background-color: #545b62; 
        }
    """,
    'success': """
        QPushButton { 
            background-color: #28a745; 
            color: white; 
            border: none; 
            min-height: 25px; 
            border-radius: 4px;
        } 
        QPushButton:hover { 
            background-color: #218838; 
        }
    """,
    'danger': """
        QPushButton { 
            background-color: #dc3545; 
            color: white; 
            border: none; 
            min-height: 25px; 
            border-radius: 4px;
        } 
        QPushButton:hover { 
            background-color: #c82333; 
        }
    """
}

# 图标配置
ICONS = {
    'open_file': "📁",
    'ocr_start': "🔍",
    'ocr_running': "🔄",
    'ocr_unavailable': "❌",
    'area_select': "📐",
    'export': "📊",
    'clear': "🗑️",
    'settings': "⚙️",
    'success': "✅",
    'warning': "⚠️",
    'error': "❌",
    'info': "ℹ️"
}

# 快捷键配置
SHORTCUTS = {
    'open_file': "Ctrl+O",
    'start_ocr': "Ctrl+R",
    'area_select': "Q",
    'save': "Ctrl+S",
    'export': "Ctrl+E",
    'zoom_in': "Ctrl++",
    'zoom_out': "Ctrl+-",
    'zoom_fit': "Ctrl+0",
    'delete': "Delete",
    'escape': "Escape"
}

# 颜色配置
COLORS = {
    'primary': '#007bff',
    'secondary': '#6c757d',
    'success': '#28a745',
    'danger': '#dc3545',
    'warning': '#ffc107',
    'info': '#17a2b8',
    'light': '#f8f9fa',
    'dark': '#343a40',
    'white': '#ffffff',
    'background': '#f5f5f5',
    'border': '#dee2e6',
    'text': '#212529',
    'text_secondary': '#6c757d'
}

# 字体配置
FONTS = {
    'default_family': "Microsoft YaHei, Arial, sans-serif",
    'default_size': 9,
    'title_size': 12,
    'small_size': 8,
    'large_size': 14
}

# 间距配置
SPACING = {
    'small': 3,
    'medium': 5,
    'large': 10,
    'extra_large': 15
}

# 边距配置
MARGINS = {
    'small': (3, 3, 3, 3),
    'medium': (5, 5, 5, 5),
    'large': (10, 10, 10, 10),
    'zero': (0, 0, 0, 0)
}

# 控件尺寸配置
WIDGET_SIZES = {
    'button_min_height': 25,
    'input_min_width': 80,
    'slider_max_width': 80,
    'label_min_width': 40,
    'progress_bar_height': 15,
    'spinbox_max_width': 60
}

# 分割器配置
SPLITTER_CONFIG = {
    'handle_width': 3,
    'handle_color': '#adb5bd',
    'handle_hover_color': '#6c757d'
}

# 表格配置
TABLE_CONFIG = {
    'row_height': 30,
    'header_height': 35,
    'column_widths': {
        'id': 50,
        'text': 200,
        'dimension': 100,
        'tolerance': 120,
        'type': 80,
        'status': 60
    }
}

# 对话框配置
DIALOG_CONFIG = {
    'min_width': 400,
    'min_height': 300,
    'button_spacing': 10,
    'content_margin': 20
}

# 工具提示配置
TOOLTIP_CONFIG = {
    'show_delay': 500,  # 毫秒
    'hide_delay': 3000,  # 毫秒
    'max_width': 300
}

# 动画配置
ANIMATION_CONFIG = {
    'duration_fast': 150,    # 毫秒
    'duration_normal': 250,  # 毫秒
    'duration_slow': 500,    # 毫秒
    'easing_curve': 'OutCubic'
}

# 缩放配置
ZOOM_CONFIG = {
    'min_factor': 0.1,
    'max_factor': 10.0,
    'step_factor': 1.2,
    'wheel_step': 0.1
}

# 网格配置
GRID_CONFIG = {
    'size': 20,
    'color': '#e0e0e0',
    'line_width': 1,
    'enabled': False
}
