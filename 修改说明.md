# PDF识别功能导出文件名修复说明

## 问题描述

在PDF识别功能完成后，导出的标注列表文件使用的是临时转换文件的名称，而不是原始PDF文件的名称。

### 修改前的问题

```python
# 错误的逻辑 - 使用临时文件路径
default_filename = f"{Path(self.current_file_path).stem}_标注列表.xlsx"
```

当处理PDF文件时：
- 原始PDF文件：`机械图纸.pdf`
- 临时转换文件：`pdf_convert_abc123_page0.png`
- 导出文件名：`pdf_convert_abc123_page0_标注列表.xlsx` ❌

## 解决方案

### 修改后的逻辑

```python
# 正确的逻辑 - 优先使用原始PDF文件名
if self.pdf_file_path:
    # PDF模式下使用原始PDF文件名
    pdf_name = Path(self.pdf_file_path).stem
    default_filename = f"{pdf_name}_标注列表.xlsx"
elif self.current_file_path:
    # 普通图片模式使用当前文件名
    default_filename = f"{Path(self.current_file_path).stem}_标注列表.xlsx"
else:
    default_filename = "标注列表.xlsx"
```

### 修改后的效果

当处理PDF文件时：
- 原始PDF文件：`机械图纸.pdf`
- 临时转换文件：`pdf_convert_abc123_page0.png`
- 导出文件名：`机械图纸_标注列表.xlsx` ✅

## 修改的文件和位置

### 1. export_to_excel 方法
**文件**: `ui/main_window.py`
**行号**: 668-677

### 2. export_to_template 方法
**文件**: `ui/main_window.py`
**行号**: 750-759

## 测试结果

```
=== 测试文件名生成逻辑 ===
原始PDF路径: C:/Users/<USER>/Documents/机械图纸.pdf
临时文件路径: C:/Temp/pdf_convert_abc123_page0.png

✅ PDF模式 - Excel导出文件名: 机械图纸_标注列表.xlsx
✅ PDF模式 - 模板导出文件名: 机械图纸_检验报告.xlsx

❌ 修改前的错误逻辑:
   Excel导出文件名: pdf_convert_abc123_page0_标注列表.xlsx
   模板导出文件名: pdf_convert_abc123_page0_检验报告.xlsx
```

## 兼容性

修改后的代码完全向后兼容：

1. **PDF模式**: 使用原始PDF文件名 ✅
2. **图片模式**: 使用图片文件名 ✅
3. **无文件模式**: 使用默认文件名 ✅

## 建议的测试步骤

1. 打开一个PDF文件进行识别
2. 添加一些标注
3. 尝试导出标注列表（Excel格式）
4. 验证导出的文件名是否使用了原始PDF文件名
5. 尝试导出到模板
6. 验证模板导出的文件名是否正确
7. 测试普通图片文件的导出功能确保仍然正常工作

## 总结

这个修改解决了PDF识别功能中导出文件名不正确的问题，现在导出的标注列表文件将使用有意义的原始PDF文件名，而不是临时转换文件的随机名称。
