# MainWindow 拆分实施指南

## 第一阶段：基础拆分实施

### 1.1 创建目录结构

```bash
# 在项目根目录执行
mkdir -p ui/components ui/controllers ui/services ui/utils
touch ui/components/__init__.py ui/controllers/__init__.py
touch ui/services/__init__.py ui/utils/__init__.py
```

### 1.2 创建 UI 构建器

**文件**: `ui/components/ui_builder.py`

```python
#!/usr/bin/env python3
"""
UI构建器模块 - 负责界面布局和控件创建
"""

from PySide6.QtWidgets import *
from PySide6.QtCore import Qt
from PySide6.QtGui import QColor

from utils.constants import UI_COLORS, DEFAULT_OCR_LANGUAGES, OCR_FILTER_OPTIONS
from utils.dependencies import HAS_OCR_SUPPORT, HAS_GPU_SUPPORT

class UIBuilder:
    """UI构建器 - 负责创建和组织界面元素"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        
    def build_main_layout(self) -> QWidget:
        """构建主布局"""
        central_widget = QWidget()
        layout = QHBoxLayout(central_widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 创建分割器
        main_splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(main_splitter)
        
        # 创建左侧面板
        left_splitter = self._create_left_panel()
        
        # 创建右侧面板
        right_panel = self._create_right_panel()
        
        # 组织主分割器
        main_splitter.addWidget(left_splitter)
        main_splitter.addWidget(right_panel)
        main_splitter.setStretchFactor(0, 3)
        main_splitter.setStretchFactor(1, 1)
        
        return central_widget
    
    def _create_left_panel(self) -> QSplitter:
        """创建左侧面板"""
        left_splitter = QSplitter(Qt.Vertical)
        
        # 图形视图面板
        graphics_panel = self._create_graphics_panel()
        
        # 标注面板
        annotation_panel = self._create_annotation_panel()
        
        left_splitter.addWidget(graphics_panel)
        left_splitter.addWidget(annotation_panel)
        left_splitter.setStretchFactor(0, 3)
        left_splitter.setStretchFactor(1, 1)
        
        return left_splitter
    
    def _create_graphics_panel(self) -> QWidget:
        """创建图形视图面板"""
        graphics_panel = QWidget()
        graphics_layout = QVBoxLayout(graphics_panel)
        graphics_layout.setContentsMargins(0, 0, 0, 0)
        graphics_layout.setSpacing(0)
        
        # 添加标题
        title = QLabel("图纸视图 & OCR识别")
        title.setStyleSheet(f"QLabel {{ background-color: {UI_COLORS['primary']}; color: white; font-weight: bold; padding: 8px; margin: 0px; border: none; }}")
        graphics_layout.addWidget(title)
        
        # OCR面板
        ocr_panel = self._create_ocr_panel()
        graphics_layout.addWidget(ocr_panel)
        
        # 图形视图将在主窗口中添加
        
        return graphics_panel
    
    def _create_annotation_panel(self) -> QWidget:
        """创建标注面板"""
        annotation_panel = QWidget()
        annotation_layout = QVBoxLayout(annotation_panel)
        annotation_layout.setContentsMargins(0, 0, 0, 0)
        annotation_layout.setSpacing(0)
        
        # 标题
        title = QLabel("标注列表")
        title.setStyleSheet(f"QLabel {{ background-color: {UI_COLORS['secondary']}; color: white; font-weight: bold; padding: 8px; margin: 0px; border: none; }}")
        annotation_layout.addWidget(title)
        
        # 标注表格将在主窗口中添加
        
        return annotation_panel
    
    def _create_right_panel(self) -> QWidget:
        """创建右侧属性面板"""
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(0)
        
        # 标题
        title = QLabel("属性编辑器")
        title.setStyleSheet(f"QLabel {{ background-color: {UI_COLORS['success']}; color: white; font-weight: bold; padding: 8px; margin: 0px; border: none; }}")
        right_layout.addWidget(title)
        
        # 属性编辑器将在主窗口中添加
        
        return right_panel
    
    def _create_ocr_panel(self) -> QWidget:
        """创建OCR控制面板"""
        ocr_widget = QWidget()
        ocr_widget.setMaximumHeight(200)
        ocr_layout = QVBoxLayout(ocr_widget)
        ocr_layout.setContentsMargins(5, 5, 5, 5)
        ocr_layout.setSpacing(3)
        
        # 第一行：语言和置信度
        row1_layout = QHBoxLayout()
        row1_layout.addWidget(QLabel("语言:"))
        
        language_combo = QComboBox()
        language_combo.addItems(list(DEFAULT_OCR_LANGUAGES.keys()))
        language_combo.setCurrentText("中文+英文")
        row1_layout.addWidget(language_combo)
        
        row1_layout.addWidget(QLabel("置信度:"))
        
        confidence_slider = QSlider(Qt.Horizontal)
        confidence_slider.setRange(10, 90)
        confidence_slider.setValue(30)
        confidence_slider.setMaximumWidth(80)
        row1_layout.addWidget(confidence_slider)
        
        confidence_label = QLabel("0.30")
        confidence_label.setMinimumWidth(40)
        row1_layout.addWidget(confidence_label)
        
        ocr_layout.addLayout(row1_layout)
        
        # 第二行：选项
        row2_layout = QHBoxLayout()
        
        enhance_contrast_cb = QCheckBox("增强对比度")
        enhance_contrast_cb.setChecked(True)
        row2_layout.addWidget(enhance_contrast_cb)
        
        denoise_cb = QCheckBox("降噪")
        denoise_cb.setChecked(True)
        row2_layout.addWidget(denoise_cb)
        
        gpu_checkbox = QCheckBox("GPU")
        gpu_checkbox.setChecked(HAS_GPU_SUPPORT)
        gpu_checkbox.setEnabled(HAS_GPU_SUPPORT)
        row2_layout.addWidget(gpu_checkbox)
        
        cpu_checkbox = QCheckBox("CPU")
        cpu_checkbox.setChecked(not HAS_GPU_SUPPORT)
        row2_layout.addWidget(cpu_checkbox)
        
        row2_layout.addWidget(QLabel("线程数:"))
        
        threads_spinbox = QSpinBox()
        threads_spinbox.setMinimum(1)
        threads_spinbox.setMaximum(32)
        threads_spinbox.setValue(8)
        threads_spinbox.setToolTip("CPU模式下使用的线程数")
        threads_spinbox.setEnabled(not HAS_GPU_SUPPORT)
        row2_layout.addWidget(threads_spinbox)
        
        row2_layout.addStretch()
        ocr_layout.addLayout(row2_layout)
        
        # 第三行：按钮
        row3_layout = QHBoxLayout()
        
        ocr_button = QPushButton("🔍 开始OCR识别" if HAS_OCR_SUPPORT else "❌ OCR不可用")
        if not HAS_OCR_SUPPORT:
            ocr_button.setEnabled(False)
            ocr_button.setToolTip("请安装完整依赖包以启用OCR功能")
        
        ocr_button.setStyleSheet(f"""
            QPushButton {{ 
                background-color: {UI_COLORS["primary"]}; 
                color: white; 
                font-weight: bold; 
                border: none; 
                min-height: 25px; 
            }} 
            QPushButton:hover {{ 
                background-color: {UI_COLORS["secondary"]}; 
            }} 
            QPushButton:disabled {{ 
                background-color: #cccccc; 
                color: #666666; 
            }}
        """)
        row3_layout.addWidget(ocr_button)
        
        create_all_btn = QPushButton("全部标注")
        create_all_btn.setMaximumWidth(80)
        row3_layout.addWidget(create_all_btn)
        
        clear_ocr_btn = QPushButton("清除OCR")
        clear_ocr_btn.setMaximumWidth(80)
        row3_layout.addWidget(clear_ocr_btn)
        
        ocr_layout.addLayout(row3_layout)
        
        # 进度条
        progress_bar = QProgressBar()
        progress_bar.setVisible(False)
        progress_bar.setMaximumHeight(15)
        ocr_layout.addWidget(progress_bar)
        
        # 统计标签
        stats_label = QLabel("识别结果: 0个文本")
        stats_label.setStyleSheet("QLabel { background-color: transparent; border: none; padding: 4px; color: #6c757d; font-size: 11px; }")
        ocr_layout.addWidget(stats_label)
        
        # 筛选
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("筛选:"))
        
        filter_combo = QComboBox()
        filter_combo.addItems(OCR_FILTER_OPTIONS)
        filter_layout.addWidget(filter_combo)
        filter_layout.addStretch()
        
        ocr_layout.addLayout(filter_layout)
        
        # 保存控件引用到主窗口
        self.main_window.language_combo = language_combo
        self.main_window.confidence_slider = confidence_slider
        self.main_window.confidence_label = confidence_label
        self.main_window.enhance_contrast_cb = enhance_contrast_cb
        self.main_window.denoise_cb = denoise_cb
        self.main_window.gpu_checkbox = gpu_checkbox
        self.main_window.cpu_checkbox = cpu_checkbox
        self.main_window.threads_spinbox = threads_spinbox
        self.main_window.ocr_button = ocr_button
        self.main_window.create_all_btn = create_all_btn
        self.main_window.clear_ocr_btn = clear_ocr_btn
        self.main_window.progress_bar = progress_bar
        self.main_window.ocr_stats_label = stats_label
        self.main_window.filter_combo = filter_combo
        
        return ocr_widget
    
    def apply_styles(self):
        """应用全局样式"""
        self.main_window.setStyleSheet(f"""
            QMainWindow {{ background-color: {UI_COLORS["background"]}; }}
            QSplitter::handle {{ background-color: {UI_COLORS["border"]}; width: 3px; height: 3px; }}
            QSplitter::handle:hover {{ background-color: #adb5bd; }}
            QLabel {{ font-weight: bold; color: {UI_COLORS["text"]}; padding: 5px; background-color: #e9ecef; border-bottom: 1px solid {UI_COLORS["border"]}; }}
            QWidget {{ font-family: "Microsoft YaHei", "Arial", sans-serif; color: {UI_COLORS["text"]}; background-color: {UI_COLORS["white"]}; }}
            QPushButton {{ background-color: {UI_COLORS["white"]}; border: 1px solid #ced4da; border-radius: 4px; padding: 8px 15px; min-height: 20px; color: {UI_COLORS["text_secondary"]}; }}
            QPushButton:hover {{ background-color: {UI_COLORS["background"]}; border-color: #6c757d; color: {UI_COLORS["text"]}; }}
            QPushButton:pressed {{ background-color: #e9ecef; }}
            QPushButton:disabled {{ background-color: #e9ecef; color: #6c757d; border-color: {UI_COLORS["border"]}; }}
            QComboBox {{ background-color: {UI_COLORS["white"]}; border: 1px solid #ced4da; border-radius: 3px; padding: 4px 8px; color: {UI_COLORS["text_secondary"]}; }}
            QComboBox:hover {{ border-color: #6c757d; }}
            QCheckBox {{ color: {UI_COLORS["text_secondary"]}; }}
            QSlider::groove:horizontal {{ background-color: {UI_COLORS["border"]}; height: 8px; border-radius: 4px; }}
            QSlider::handle:horizontal {{ background-color: #6c757d; border: 1px solid {UI_COLORS["text_secondary"]}; width: 18px; border-radius: 9px; margin: -5px 0; }}
            QSlider::handle:horizontal:hover {{ background-color: {UI_COLORS["text_secondary"]}; }}
        """)
```

### 1.3 创建菜单工具栏模块

**文件**: `ui/components/menu_toolbar.py`

```python
#!/usr/bin/env python3
"""
菜单和工具栏模块
"""

from PySide6.QtWidgets import QMenuBar, QToolBar, QMenu
from PySide6.QtGui import QAction, QKeySequence
from PySide6.QtCore import Qt

class MenuToolbarBuilder:
    """菜单和工具栏构建器"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        
    def setup_menu_bar(self):
        """设置菜单栏"""
        menubar = self.main_window.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 打开文件
        open_action = QAction("打开文件(&O)", self.main_window)
        open_action.setShortcut(QKeySequence.Open)
        open_action.triggered.connect(self.main_window.open_file)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        # 导出菜单
        export_menu = file_menu.addMenu("导出(&E)")
        
        export_excel_action = QAction("导出到Excel(&X)", self.main_window)
        export_excel_action.triggered.connect(self.main_window.export_to_excel)
        export_menu.addAction(export_excel_action)
        
        export_template_action = QAction("导出到模板(&T)", self.main_window)
        export_template_action.triggered.connect(self.main_window.export_to_template)
        export_menu.addAction(export_template_action)
        
        export_image_action = QAction("导出标注图片(&I)", self.main_window)
        export_image_action.triggered.connect(self.main_window.export_annotated_image)
        export_menu.addAction(export_image_action)
        
        # 编辑菜单
        edit_menu = menubar.addMenu("编辑(&E)")
        
        clear_annotations_action = QAction("清除所有标注(&C)", self.main_window)
        clear_annotations_action.triggered.connect(self.main_window.clear_annotations)
        edit_menu.addAction(clear_annotations_action)
        
        reorder_action = QAction("重新排序标注(&R)", self.main_window)
        reorder_action.triggered.connect(self.main_window.reorder_annotations)
        edit_menu.addAction(reorder_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")
        
        ocr_action = QAction("开始OCR识别(&O)", self.main_window)
        ocr_action.setShortcut("Ctrl+R")
        ocr_action.triggered.connect(self.main_window.start_ocr_recognition)
        tools_menu.addAction(ocr_action)
        
        area_ocr_action = QAction("区域OCR识别(&A)", self.main_window)
        area_ocr_action.setShortcut("Q")
        area_ocr_action.setCheckable(True)
        area_ocr_action.triggered.connect(self.main_window.toggle_area_selection)
        tools_menu.addAction(area_ocr_action)
        
        # 保存动作引用
        self.main_window.area_select_action = area_ocr_action
        
    def setup_toolbar(self):
        """设置工具栏"""
        toolbar = self.main_window.addToolBar("主工具栏")
        toolbar.setMovable(False)
        
        # 打开文件
        open_action = QAction("📁 打开", self.main_window)
        open_action.setToolTip("打开图片或PDF文件")
        open_action.triggered.connect(self.main_window.open_file)
        toolbar.addAction(open_action)
        
        toolbar.addSeparator()
        
        # OCR识别
        ocr_action = QAction("🔍 OCR", self.main_window)
        ocr_action.setToolTip("开始OCR文字识别")
        ocr_action.triggered.connect(self.main_window.start_ocr_recognition)
        toolbar.addAction(ocr_action)
        
        # 区域选择
        area_action = QAction("📐 区域", self.main_window)
        area_action.setToolTip("选择区域进行OCR识别 (快捷键: Q)")
        area_action.setCheckable(True)
        area_action.triggered.connect(self.main_window.toggle_area_selection)
        toolbar.addAction(area_action)
        
        toolbar.addSeparator()
        
        # 导出
        export_action = QAction("📊 导出", self.main_window)
        export_action.setToolTip("导出标注数据到Excel")
        export_action.triggered.connect(self.main_window.export_to_excel)
        toolbar.addAction(export_action)
        
        # 保存工具栏动作引用
        self.main_window.area_select_action = area_action
```

## 第二阶段：控制器拆分

### 2.1 修改主窗口

**文件**: `ui/main_window.py` (修改后的版本)

```python
#!/usr/bin/env python3
"""
主窗口模块 - 重构后的协调器版本
"""

import logging
from PySide6.QtWidgets import QMainWindow, QGraphicsScene
from PySide6.QtCore import QThreadPool, Qt
from PySide6.QtGui import QIcon

# 导入组件
from ui.components.ui_builder import UIBuilder
from ui.components.menu_toolbar import MenuToolbarBuilder
from ui.graphics_view import GraphicsView
from ui.annotation_list import AnnotationTable
from ui.property_editor import PropertyEditor

# 导入控制器
from ui.controllers.ocr_controller import OCRController
from ui.controllers.annotation_controller import AnnotationController
from ui.controllers.file_controller import FileController

# 导入服务
from ui.services.data_service import DataService
from ui.services.state_service import StateService

# 导入管理器
from ui.managers.export_manager import ExportManager
from ui.managers.file_manager import FileManager

logger = logging.getLogger('PyQtBubble')

class MainWindow(QMainWindow):
    """主窗口协调器 - 负责组件协调和生命周期管理"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("图纸标注系统")
        self.setWindowIcon(QIcon("assets/icon.ico"))
        self.setMinimumSize(1200, 800)
        self.resize(1600, 900)
        
        # 创建基础组件
        self.thread_pool = QThreadPool()
        self.graphics_scene = QGraphicsScene()
        
        # 创建服务层
        self.data_service = DataService()
        self.state_service = StateService()
        
        # 创建控制器
        self.ocr_controller = OCRController(self)
        self.annotation_controller = AnnotationController(self)
        self.file_controller = FileController(self)
        
        # 创建管理器（保持向后兼容）
        self.export_manager = ExportManager(self)
        self.file_manager = FileManager(self)
        
        # 初始化UI
        self.setup_ui()
        self.setup_connections()
        
        logger.debug("MainWindow初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建UI构建器
        ui_builder = UIBuilder(self)
        
        # 应用样式
        ui_builder.apply_styles()
        
        # 构建主布局
        central_widget = ui_builder.build_main_layout()
        self.setCentralWidget(central_widget)
        
        # 创建核心组件
        self.graphics_view = GraphicsView()
        self.graphics_view.setScene(self.graphics_scene)
        
        self.annotation_table = AnnotationTable()
        self.property_editor = PropertyEditor(self)
        
        # 将组件添加到布局中
        # (这里需要根据实际的布局结构来添加)
        
        # 创建状态栏
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("就绪", 2000)
        
        # 设置菜单和工具栏
        menu_toolbar_builder = MenuToolbarBuilder(self)
        menu_toolbar_builder.setup_menu_bar()
        menu_toolbar_builder.setup_toolbar()
    
    def setup_connections(self):
        """设置信号连接"""
        # 控制器间信号连接
        self.ocr_controller.ocr_finished.connect(
            self.annotation_controller.on_ocr_finished
        )
        
        self.annotation_controller.annotation_created.connect(
            self.data_service.add_annotation_data
        )
        
        # UI组件信号连接
        self.annotation_table.annotation_selected.connect(
            self.annotation_controller.select_annotation_by_id
        )
        
        # 其他信号连接...
    
    # 向后兼容的委托方法
    def open_file(self):
        """打开文件 - 委托给文件控制器"""
        return self.file_controller.open_file()
    
    def start_ocr_recognition(self):
        """开始OCR识别 - 委托给OCR控制器"""
        return self.ocr_controller.start_recognition()
    
    def create_annotation_from_ocr_result(self, ocr_result: dict):
        """从OCR结果创建标注 - 委托给标注控制器"""
        return self.annotation_controller.create_annotation_from_ocr(ocr_result)
    
    # 其他委托方法...
```

这个实施指南提供了具体的代码示例和分步骤的重构方案。通过这种方式，可以逐步将3157行的巨大文件拆分成多个职责清晰、易于维护的小模块。
