#!/usr/bin/env python3
"""
第一阶段功能验证脚本 - 工具函数提取
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PySide6.QtCore import QPointF, QRectF, QSize
from PySide6.QtGui import QPixmap

def verify_coordinate_utils():
    """验证坐标工具函数"""
    print("🔍 验证坐标工具函数...")
    
    try:
        from ui.utils.coordinate_utils import (
            calculate_distance_between_points,
            get_rect_center,
            normalize_rect,
            calculate_bbox_center,
            convert_bbox_to_qpointf_list,
            is_vertical_rect
        )
        
        # 测试距离计算
        p1, p2 = QPointF(0, 0), QPointF(3, 4)
        distance = calculate_distance_between_points(p1, p2)
        assert abs(distance - 5.0) < 0.01, f"距离计算错误: {distance}"
        print("  ✅ 距离计算正确")
        
        # 测试矩形中心
        rect = QRectF(10, 20, 100, 200)
        center = get_rect_center(rect)
        assert center.x() == 60 and center.y() == 120, f"中心计算错误: {center}"
        print("  ✅ 矩形中心计算正确")
        
        # 测试矩形标准化
        rect = QRectF(10, 10, -5, -5)  # 负宽高
        normalized = normalize_rect(rect)
        assert normalized.width() >= 0 and normalized.height() >= 0, "矩形标准化失败"
        print("  ✅ 矩形标准化正确")
        
        # 测试边界框中心
        bbox_points = [QPointF(0, 0), QPointF(10, 0), QPointF(10, 10), QPointF(0, 10)]
        bbox_center = calculate_bbox_center(bbox_points)
        assert bbox_center.x() == 5 and bbox_center.y() == 5, f"边界框中心错误: {bbox_center}"
        print("  ✅ 边界框中心计算正确")
        
        # 测试坐标转换
        bbox = [[0, 0], [10, 0], [10, 10], [0, 10]]
        qpoint_list = convert_bbox_to_qpointf_list(bbox)
        assert len(qpoint_list) == 4, "坐标转换失败"
        assert qpoint_list[0].x() == 0 and qpoint_list[0].y() == 0, "坐标转换错误"
        print("  ✅ 坐标转换正确")
        
        # 测试竖直矩形判断
        vertical_rect = QRectF(0, 0, 50, 150)  # 高宽比 = 3
        horizontal_rect = QRectF(0, 0, 150, 50)  # 高宽比 = 0.33
        assert is_vertical_rect(vertical_rect), "竖直矩形判断错误"
        assert not is_vertical_rect(horizontal_rect), "水平矩形判断错误"
        print("  ✅ 矩形方向判断正确")
        
        print("✅ 坐标工具函数验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 坐标工具函数验证失败: {e}")
        return False

def verify_text_utils():
    """验证文本工具函数"""
    print("🔍 验证文本工具函数...")
    
    try:
        from ui.utils.text_utils import (
            clean_ocr_text,
            format_dimension_text,
            extract_numeric_value,
            is_dimension_text,
            is_thread_specification,
            is_tolerance_text,
            parse_annotation_text,
            parse_tolerance_values
        )
        
        # 测试文本清理
        cleaned = clean_ocr_text("  Ø25  mm  ")
        assert cleaned == "Ø25 mm", f"文本清理错误: '{cleaned}'"
        print("  ✅ 文本清理正确")
        
        # 测试尺寸格式化
        formatted = format_dimension_text("Ø25.5mm")
        assert "Ø" in formatted, f"尺寸格式化错误: '{formatted}'"
        print("  ✅ 尺寸格式化正确")
        
        # 测试数值提取
        value = extract_numeric_value("Ø25.5")
        assert value == 25.5, f"数值提取错误: {value}"
        print("  ✅ 数值提取正确")
        
        # 测试尺寸识别
        assert is_dimension_text("25.5"), "尺寸识别失败"
        assert is_dimension_text("Ø25"), "直径识别失败"
        assert is_dimension_text("R10"), "半径识别失败"
        print("  ✅ 尺寸识别正确")
        
        # 测试螺纹识别
        assert is_thread_specification("M8×1.25"), "螺纹识别失败"
        assert is_thread_specification("M10"), "螺纹识别失败"
        print("  ✅ 螺纹识别正确")
        
        # 测试公差识别
        assert is_tolerance_text("+0.1"), "正公差识别失败"
        assert is_tolerance_text("-0.05"), "负公差识别失败"
        assert is_tolerance_text("±0.02"), "对称公差识别失败"
        print("  ✅ 公差识别正确")
        
        # 测试公差解析
        upper, lower = parse_tolerance_values("±0.1")
        assert upper == "+0.1" and lower == "-0.1", f"对称公差解析错误: {upper}, {lower}"
        print("  ✅ 公差解析正确")
        
        # 测试标注文本解析
        parsed = parse_annotation_text("Ø25\n+0.1\n-0.05")
        assert parsed['dimension'] == "25", f"尺寸解析错误: {parsed['dimension']}"
        assert parsed['dimension_type'] == "diameter", f"尺寸类型错误: {parsed['dimension_type']}"
        assert parsed['upper_tolerance'] == "+0.1", f"上公差错误: {parsed['upper_tolerance']}"
        assert parsed['lower_tolerance'] == "-0.05", f"下公差错误: {parsed['lower_tolerance']}"
        print("  ✅ 标注文本解析正确")
        
        print("✅ 文本工具函数验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 文本工具函数验证失败: {e}")
        return False

def verify_image_utils():
    """验证图像工具函数"""
    print("🔍 验证图像工具函数...")
    
    try:
        from ui.utils.image_utils import (
            calculate_scale_factor,
            estimate_memory_usage,
            get_image_aspect_ratio,
            is_landscape_image,
            is_portrait_image,
            validate_image_file
        )
        
        # 测试缩放因子计算
        image_size = QSize(1000, 800)
        target_size = QSize(500, 400)
        scale_factor = calculate_scale_factor(image_size, target_size)
        assert scale_factor == 0.5, f"缩放因子计算错误: {scale_factor}"
        print("  ✅ 缩放因子计算正确")
        
        # 测试内存估算
        memory = estimate_memory_usage(1920, 1080, 4)
        expected = 1920 * 1080 * 4
        assert memory == expected, f"内存估算错误: {memory}"
        print("  ✅ 内存估算正确")
        
        # 测试宽高比
        ratio = get_image_aspect_ratio(1920, 1080)
        assert abs(ratio - (1920/1080)) < 0.01, f"宽高比计算错误: {ratio}"
        print("  ✅ 宽高比计算正确")
        
        # 测试图像方向判断
        assert is_landscape_image(1920, 1080), "横向图像判断错误"
        assert is_portrait_image(1080, 1920), "纵向图像判断错误"
        print("  ✅ 图像方向判断正确")
        
        # 测试文件验证（使用不存在的文件）
        valid, error = validate_image_file("nonexistent.jpg")
        assert not valid, "不存在文件应该验证失败"
        assert "不存在" in error, f"错误信息不正确: {error}"
        print("  ✅ 文件验证正确")
        
        print("✅ 图像工具函数验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 图像工具函数验证失败: {e}")
        return False

def verify_mainwindow_integration():
    """验证MainWindow集成"""
    print("🔍 验证MainWindow集成...")
    
    try:
        # 测试导入
        from ui.main_window import MainWindow
        print("  ✅ MainWindow导入成功")
        
        # 这里可以添加更多集成测试
        # 但由于需要Qt应用程序上下文，暂时只测试导入
        
        print("✅ MainWindow集成验证通过")
        return True
        
    except Exception as e:
        print(f"❌ MainWindow集成验证失败: {e}")
        return False

def main():
    """主验证函数"""
    print("=" * 50)
    print("🚀 开始第一阶段验证：工具函数提取")
    print("=" * 50)
    
    results = []
    
    # 验证各个模块
    results.append(verify_coordinate_utils())
    results.append(verify_text_utils())
    results.append(verify_image_utils())
    results.append(verify_mainwindow_integration())
    
    print("\n" + "=" * 50)
    print("📊 验证结果汇总")
    print("=" * 50)
    
    success_count = sum(results)
    total_count = len(results)
    
    if success_count == total_count:
        print(f"🎉 全部验证通过！({success_count}/{total_count})")
        print("✅ 第一阶段工具函数提取成功完成")
        print("📝 代码结构更加模块化，为后续重构奠定基础")
        return True
    else:
        print(f"⚠️ 部分验证失败 ({success_count}/{total_count})")
        print("❌ 需要修复问题后再继续")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
