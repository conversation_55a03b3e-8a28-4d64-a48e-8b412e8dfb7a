#!/usr/bin/env python3
"""
PDF加载工作线程模块
"""

import time
import logging
from PySide6.QtWidgets import QGraphicsScene
from PySide6.QtCore import QObject, QRunnable, Signal, QEvent
from PySide6.QtGui import QPixmap

from core.file_loader import FileLoader

# 获取logger
logger = logging.getLogger('PyQtBubble')

# 添加一个自定义事件类用于从线程传递加载结果到主线程
class LoadPDFEvent(QEvent):
    """自定义事件用于将PDF加载结果从线程传递到主线程"""
    EVENT_TYPE = QEvent.Type(QEvent.registerEventType())
    
    def __init__(self, pixmap, temp_path):
        super().__init__(self.EVENT_TYPE)
        self.pixmap = pixmap
        self.temp_path = temp_path

class PDFLoaderSignals(QObject):
    """PDF加载工作线程的信号类"""
    finished = Signal(QPixmap, str)  # 成功加载后发出信号：pixmap, 临时文件路径
    error = Signal(str)  # 加载出错时发出信号
    progress = Signal(int)  # 加载进度信号

class PDFLoaderWorker(QRunnable):
    """PDF加载工作线程"""
    def __init__(self, pdf_path, page_index, quality=4.0, force_resolution=False):
        super().__init__()
        self.pdf_path = pdf_path
        self.page_index = page_index
        self.quality = quality
        self.force_resolution = force_resolution
        self.signals = PDFLoaderSignals()
        
    def run(self):
        """执行PDF加载"""
        try:
            start_time = time.time()
            logger.debug(f"开始加载PDF页面: {self.page_index+1}, 质量: {self.quality}, 强制分辨率: {self.force_resolution}")
            
            self.signals.progress.emit(10)
            # 创建一个临时场景，用于在线程中处理
            temp_scene = QGraphicsScene()
            logger.debug(f"创建临时场景耗时: {time.time() - start_time:.2f}秒")
            
            self.signals.progress.emit(30)
            
            # 调用FileLoader加载PDF
            logger.debug(f"开始调用FileLoader.load_pdf...")
            load_start = time.time()
            pixmap, temp_path = FileLoader.load_pdf(
                self.pdf_path, temp_scene, self.page_index, quality=self.quality,
                force_resolution=self.force_resolution
            )
            logger.debug(f"FileLoader.load_pdf完成，耗时: {time.time() - load_start:.2f}秒")
            
            self.signals.progress.emit(90)
            
            if pixmap and not pixmap.isNull() and temp_path:
                logger.debug(f"PDF页面加载成功: {self.page_index+1}, 尺寸: {pixmap.width()}x{pixmap.height()}")
                # 成功加载，发送信号
                self.signals.finished.emit(pixmap, temp_path)
            else:
                logger.error(f"PDF页面加载失败: pixmap为空或temp_path为空")
                # 加载失败
                self.signals.error.emit("无法加载PDF页面")
                
            logger.debug(f"PDF加载线程总耗时: {time.time() - start_time:.2f}秒")
                
        except Exception as e:
            logger.exception(f"PDF加载过程中发生异常: {str(e)}")
            # 处理异常
            self.signals.error.emit(str(e))
