# MainWindow 拆分重构方案

## 1. 当前文件分析

### 1.1 文件规模
- **总行数**: 3157行
- **方法数量**: 90个方法
- **主要类**: 1个 (MainWindow)
- **复杂度**: 极高，维护困难

### 1.2 功能模块识别

通过分析，识别出以下主要功能模块：

| 功能模块 | 方法数量 | 大概行数 | 职责描述 |
|---------|---------|---------|----------|
| **UI初始化** | 8个 | ~400行 | 界面布局、控件创建、样式设置 |
| **OCR处理** | 12个 | ~800行 | OCR识别、结果处理、文本分类 |
| **标注管理** | 18个 | ~900行 | 标注创建、编辑、删除、排序 |
| **文件处理** | 15个 | ~600行 | 文件加载、PDF处理、页面管理 |
| **事件处理** | 10个 | ~300行 | 键盘、鼠标、窗口事件 |
| **工具功能** | 8个 | ~200行 | 屏蔽区域、颜色选择、导出 |
| **数据管理** | 12个 | ~400行 | 数据同步、状态管理、缓存 |
| **辅助功能** | 7个 | ~200行 | 工具方法、配置管理 |

### 1.3 依赖关系分析

**高耦合问题**：
- OCR处理与标注管理紧密耦合
- 文件处理与UI更新混合
- 事件处理分散在各个功能中
- 数据管理缺乏统一接口

## 2. 拆分方案设计

### 2.1 拆分原则

1. **单一职责原则**: 每个模块只负责一个核心功能
2. **低耦合高内聚**: 模块间通过接口和信号通信
3. **保持现有架构**: 遵循现有的 ui/ 目录结构
4. **最小化影响**: 减少对其他模块的修改

### 2.2 目标文件结构

```
ui/
├── main_window.py              # 主窗口协调器 (~400行)
├── components/                 # UI组件模块
│   ├── __init__.py
│   ├── ui_builder.py          # UI构建器 (~300行)
│   ├── menu_toolbar.py        # 菜单和工具栏 (~200行)
│   └── status_panel.py        # 状态栏和导航 (~150行)
├── controllers/               # 控制器模块
│   ├── __init__.py
│   ├── ocr_controller.py      # OCR控制器 (~500行)
│   ├── annotation_controller.py # 标注控制器 (~600行)
│   ├── file_controller.py     # 文件控制器 (~400行)
│   └── event_controller.py    # 事件控制器 (~200行)
├── services/                  # 服务模块
│   ├── __init__.py
│   ├── data_service.py        # 数据管理服务 (~300行)
│   ├── state_service.py       # 状态管理服务 (~200行)
│   └── config_service.py      # 配置管理服务 (~150行)
└── utils/                     # 工具模块
    ├── __init__.py
    ├── coordinate_utils.py    # 坐标转换工具 (~100行)
    └── text_utils.py          # 文本处理工具 (~100行)
```

## 3. 详细拆分方案

### 3.1 主窗口协调器 (main_window.py)

**保留内容** (~400行):
```python
class MainWindow(QMainWindow):
    """主窗口协调器 - 只负责组件协调和生命周期管理"""
    
    def __init__(self):
        # 基础初始化
        # 创建控制器实例
        # 设置UI布局
        # 连接信号槽
    
    # 生命周期方法
    def setup_ui(self)
    def setup_connections(self)
    def keyPressEvent(self, event)
    def resizeEvent(self, event)
    def event(self, event)
    
    # 控制器委托方法
    def open_file(self) -> self.file_controller.open_file()
    def start_ocr_recognition(self) -> self.ocr_controller.start_recognition()
    def create_annotation(self) -> self.annotation_controller.create_annotation()
```

### 3.2 UI构建器 (ui/components/ui_builder.py)

**功能职责** (~300行):
- 界面布局创建
- 控件初始化
- 样式设置
- 面板组织

**主要方法**:
```python
class UIBuilder:
    def build_main_layout(self, parent) -> QWidget
    def create_graphics_panel(self) -> QWidget
    def create_annotation_panel(self) -> QWidget
    def create_property_panel(self) -> QWidget
    def setup_compact_ocr_panel(self) -> QWidget
    def apply_styles(self)
```

### 3.3 OCR控制器 (ui/controllers/ocr_controller.py)

**功能职责** (~500行):
- OCR识别流程控制
- 结果处理和分类
- 区域OCR管理
- OCR配置管理

**主要方法**:
```python
class OCRController:
    def start_recognition(self)
    def start_area_recognition(self, rect: QRectF)
    def on_ocr_finished(self, results: List[dict])
    def merge_adjacent_results(self, results: List[dict])
    def classify_ocr_results(self, results: List[dict])
    def filter_ocr_results(self)
    def clear_ocr_results(self)
    def display_ocr_results(self)
```

### 3.4 标注控制器 (ui/controllers/annotation_controller.py)

**功能职责** (~600行):
- 标注创建和管理
- 标注编辑和删除
- 标注排序和重排
- 标注样式管理

**主要方法**:
```python
class AnnotationController:
    def create_annotation(self, anchor_point: QPointF, text: str)
    def create_annotation_from_ocr(self, ocr_result: dict)
    def delete_annotation(self, annotation: BubbleAnnotationItem)
    def reorder_annotations(self)
    def update_annotation_style(self, annotation, style)
    def change_annotation_size(self, percent: int)
    def audit_annotation(self, annotation: BubbleAnnotationItem)
```

### 3.5 文件控制器 (ui/controllers/file_controller.py)

**功能职责** (~400行):
- 文件加载管理
- PDF页面处理
- 多页数据管理
- 文件格式支持

**主要方法**:
```python
class FileController:
    def load_file(self, file_path: str)
    def load_pdf_page(self, page_index: int)
    def save_current_page_data(self)
    def restore_page_data(self, page_index: int)
    def convert_pdf_to_images(self)
    def go_to_next_page(self)
    def go_to_prev_page(self)
```

### 3.6 数据管理服务 (ui/services/data_service.py)

**功能职责** (~300行):
- 标注数据模型
- OCR结果管理
- 页面数据缓存
- 数据同步

**主要方法**:
```python
class DataService:
    def add_annotation_data(self, annotation: BubbleAnnotationItem)
    def remove_annotation_data(self, annotation_id: int)
    def update_annotation_data(self, annotation_id: int, data: dict)
    def get_annotations_by_page(self, page_index: int)
    def save_page_data(self, page_index: int)
    def load_page_data(self, page_index: int)
    def sync_data_model(self)
```

## 4. 拆分实施步骤

### 4.1 第一阶段：基础拆分 (优先级: 高)

1. **创建目录结构**
   ```bash
   mkdir -p ui/components ui/controllers ui/services ui/utils
   touch ui/components/__init__.py ui/controllers/__init__.py
   touch ui/services/__init__.py ui/utils/__init__.py
   ```

2. **提取UI构建器**
   - 将 `setup_ui()` 和相关UI创建方法移到 `ui_builder.py`
   - 将 `setup_compact_ocr_panel()` 移到 `ui_builder.py`
   - 更新主窗口中的调用

3. **提取菜单工具栏**
   - 将 `setup_menu_bar()` 和 `setup_toolbar()` 移到 `menu_toolbar.py`
   - 保持信号连接的完整性

### 4.2 第二阶段：控制器拆分 (优先级: 高)

1. **创建OCR控制器**
   - 移动所有OCR相关方法到 `ocr_controller.py`
   - 包括: `start_ocr_recognition()`, `on_ocr_finished()`, `merge_adjacent_ocr_results()` 等
   - 设置信号转发机制

2. **创建标注控制器**
   - 移动标注管理方法到 `annotation_controller.py`
   - 包括: `create_annotation_from_ocr_result()`, `reorder_annotations()` 等
   - 保持与UI的信号连接

### 4.3 第三阶段：服务层拆分 (优先级: 中)

1. **创建数据服务**
   - 提取数据管理逻辑到 `data_service.py`
   - 统一数据访问接口
   - 实现数据缓存机制

2. **创建状态服务**
   - 提取状态管理到 `state_service.py`
   - 统一状态更新机制

### 4.4 第四阶段：优化完善 (优先级: 低)

1. **创建工具模块**
   - 提取通用工具方法
   - 优化代码复用

2. **完善文档和测试**
   - 添加模块文档
   - 编写单元测试

## 5. 技术实施细节

### 5.1 信号-槽连接处理

**问题**: 拆分后信号连接可能断裂
**解决方案**: 使用信号转发机制

```python
# 在主窗口中设置信号转发
class MainWindow(QMainWindow):
    def setup_connections(self):
        # OCR控制器信号转发
        self.ocr_controller.ocr_finished.connect(
            self.annotation_controller.on_ocr_finished
        )
        
        # 标注控制器信号转发
        self.annotation_controller.annotation_created.connect(
            self.data_service.add_annotation_data
        )
```

### 5.2 依赖注入

**问题**: 控制器间需要相互访问
**解决方案**: 通过主窗口进行依赖注入

```python
class MainWindow(QMainWindow):
    def __init__(self):
        # 创建服务
        self.data_service = DataService()
        self.state_service = StateService()
        
        # 创建控制器并注入依赖
        self.ocr_controller = OCRController(
            data_service=self.data_service,
            state_service=self.state_service
        )
        
        self.annotation_controller = AnnotationController(
            data_service=self.data_service,
            graphics_scene=self.graphics_scene
        )
```

### 5.3 向后兼容

**问题**: 其他模块可能调用MainWindow的方法
**解决方案**: 保留委托方法

```python
class MainWindow(QMainWindow):
    # 保留原有方法签名，委托给控制器
    def start_ocr_recognition(self):
        """向后兼容方法 - 委托给OCR控制器"""
        return self.ocr_controller.start_recognition()
    
    def create_annotation_from_ocr_result(self, ocr_result: dict):
        """向后兼容方法 - 委托给标注控制器"""
        return self.annotation_controller.create_annotation_from_ocr(ocr_result)
```

## 6. 拆分优势与风险

### 6.1 优势

1. **可维护性提升**
   - 单个文件行数减少到400行以下
   - 职责清晰，易于理解和修改
   - 模块化设计便于团队协作

2. **可扩展性增强**
   - 新功能可独立开发
   - 控制器可独立测试
   - 服务层可复用

3. **代码质量改善**
   - 减少代码重复
   - 统一数据访问接口
   - 清晰的架构层次

### 6.2 潜在风险

1. **短期复杂度增加**
   - 需要理解新的架构
   - 调试可能需要跨多个文件

2. **性能影响**
   - 增加了方法调用层次
   - 信号转发可能有微小开销

3. **兼容性风险**
   - 可能影响现有的扩展代码
   - 需要更新相关文档

### 6.3 风险缓解措施

1. **渐进式重构**
   - 分阶段实施，每阶段都保持功能完整
   - 保留向后兼容的委托方法

2. **充分测试**
   - 每个阶段完成后进行全面测试
   - 保持现有功能的完整性

3. **文档更新**
   - 及时更新架构文档
   - 提供迁移指南

## 7. 实施建议

### 7.1 优先级排序

1. **高优先级**: UI构建器、OCR控制器、标注控制器
2. **中优先级**: 文件控制器、数据服务
3. **低优先级**: 状态服务、工具模块

### 7.2 实施时间估算

- **第一阶段**: 2-3天
- **第二阶段**: 3-4天  
- **第三阶段**: 2-3天
- **第四阶段**: 1-2天
- **总计**: 8-12天

### 7.3 成功标准

1. **功能完整性**: 所有现有功能正常工作
2. **性能稳定**: 不出现明显的性能退化
3. **代码质量**: 单个文件不超过500行
4. **可维护性**: 新功能开发效率提升

这个拆分方案将显著提升代码的可维护性和可扩展性，为项目的长期发展奠定良好基础。
