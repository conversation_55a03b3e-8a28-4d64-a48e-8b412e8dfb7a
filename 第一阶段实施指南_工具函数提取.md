# 第一阶段实施指南：工具函数提取

## 阶段概述

**目标**: 提取MainWindow中的独立工具函数，建立基础工具层
**风险级别**: 极低 ⭐
**预估时间**: 6小时 (工具函数4小时 + 常量配置2小时)
**成功标准**: 所有功能保持100%一致，代码更加模块化

## 步骤1: 创建目录结构 (15分钟)

```bash
# 在项目根目录执行
mkdir -p ui/utils ui/config
touch ui/utils/__init__.py ui/config/__init__.py
```

## 步骤2: 分析和识别工具函数 (30分钟)

### 2.1 扫描MainWindow中的纯函数

使用以下命令查找候选函数：
```bash
# 查找不使用self的方法（可能是纯函数）
grep -n "def.*(" ui/main_window.py | grep -v "self\."
```

### 2.2 识别的工具函数类别

**坐标转换函数**:
```python
# 在MainWindow中找到的函数
def map_scene_to_view_coordinates(self, scene_point):
def calculate_distance_between_points(self, p1, p2):
def get_rect_center(self, rect):
def normalize_rect(self, rect):
```

**文本处理函数**:
```python
def clean_ocr_text(self, text):
def format_dimension_text(self, text):
def extract_numeric_value(self, text):
def validate_tolerance_format(self, text):
```

**图像处理函数**:
```python
def create_thumbnail(self, pixmap, size):
def calculate_scale_factor(self, image_size, target_size):
def get_image_info(self, file_path):
```

## 步骤3: 创建坐标工具模块 (60分钟)

### 3.1 创建文件 `ui/utils/coordinate_utils.py`

```python
#!/usr/bin/env python3
"""
坐标转换和几何计算工具模块
"""

import math
from PySide6.QtCore import QPointF, QRectF
from PySide6.QtGui import QTransform

def map_scene_to_view_coordinates(scene_point: QPointF, transform: QTransform) -> QPointF:
    """将场景坐标转换为视图坐标"""
    if transform.isInvertible():
        inverted_transform, _ = transform.inverted()
        return inverted_transform.map(scene_point)
    return scene_point

def calculate_distance_between_points(p1: QPointF, p2: QPointF) -> float:
    """计算两点之间的距离"""
    dx = p2.x() - p1.x()
    dy = p2.y() - p1.y()
    return math.sqrt(dx * dx + dy * dy)

def get_rect_center(rect: QRectF) -> QPointF:
    """获取矩形的中心点"""
    return QPointF(
        rect.x() + rect.width() / 2,
        rect.y() + rect.height() / 2
    )

def normalize_rect(rect: QRectF) -> QRectF:
    """标准化矩形（确保宽高为正值）"""
    return rect.normalized()

def calculate_rect_area(rect: QRectF) -> float:
    """计算矩形面积"""
    return rect.width() * rect.height()

def is_point_in_rect(point: QPointF, rect: QRectF) -> bool:
    """判断点是否在矩形内"""
    return rect.contains(point)

def expand_rect(rect: QRectF, margin: float) -> QRectF:
    """扩展矩形边界"""
    return QRectF(
        rect.x() - margin,
        rect.y() - margin,
        rect.width() + 2 * margin,
        rect.height() + 2 * margin
    )

def calculate_angle_between_points(p1: QPointF, p2: QPointF) -> float:
    """计算两点之间的角度（弧度）"""
    dx = p2.x() - p1.x()
    dy = p2.y() - p1.y()
    return math.atan2(dy, dx)

def rotate_point_around_center(point: QPointF, center: QPointF, angle: float) -> QPointF:
    """围绕中心点旋转点"""
    cos_a = math.cos(angle)
    sin_a = math.sin(angle)
    
    # 平移到原点
    dx = point.x() - center.x()
    dy = point.y() - center.y()
    
    # 旋转
    new_x = dx * cos_a - dy * sin_a
    new_y = dx * sin_a + dy * cos_a
    
    # 平移回去
    return QPointF(new_x + center.x(), new_y + center.y())
```

### 3.2 在MainWindow中添加导入和wrapper方法

```python
# 在MainWindow顶部添加导入
from ui.utils.coordinate_utils import (
    map_scene_to_view_coordinates,
    calculate_distance_between_points,
    get_rect_center,
    normalize_rect
)

# 在MainWindow类中添加wrapper方法（保持向后兼容）
def map_scene_to_view_coordinates(self, scene_point):
    """向后兼容wrapper"""
    return map_scene_to_view_coordinates(scene_point, self.graphics_view.transform())

def calculate_distance_between_points(self, p1, p2):
    """向后兼容wrapper"""
    return calculate_distance_between_points(p1, p2)

def get_rect_center(self, rect):
    """向后兼容wrapper"""
    return get_rect_center(rect)

def normalize_rect(self, rect):
    """向后兼容wrapper"""
    return normalize_rect(rect)
```

### 3.3 测试坐标工具函数

```python
# 创建测试文件 tests/test_coordinate_utils.py
import unittest
from PySide6.QtCore import QPointF, QRectF
from ui.utils.coordinate_utils import *

class TestCoordinateUtils(unittest.TestCase):
    def test_distance_calculation(self):
        p1 = QPointF(0, 0)
        p2 = QPointF(3, 4)
        distance = calculate_distance_between_points(p1, p2)
        self.assertAlmostEqual(distance, 5.0, places=2)
    
    def test_rect_center(self):
        rect = QRectF(10, 20, 100, 200)
        center = get_rect_center(rect)
        self.assertEqual(center.x(), 60)  # 10 + 100/2
        self.assertEqual(center.y(), 120)  # 20 + 200/2
    
    def test_normalize_rect(self):
        rect = QRectF(10, 10, -5, -5)  # 负宽高
        normalized = normalize_rect(rect)
        self.assertGreaterEqual(normalized.width(), 0)
        self.assertGreaterEqual(normalized.height(), 0)

if __name__ == '__main__':
    unittest.main()
```

## 步骤4: 创建文本处理模块 (60分钟)

### 4.1 创建文件 `ui/utils/text_utils.py`

```python
#!/usr/bin/env python3
"""
文本处理和验证工具模块
"""

import re
from typing import Optional, Tuple

def clean_ocr_text(text: str) -> str:
    """清理OCR识别的文本"""
    if not text:
        return ""
    
    # 移除多余空白
    text = re.sub(r'\s+', ' ', text.strip())
    
    # 修正常见OCR错误
    corrections = {
        'O': '0',  # 字母O替换为数字0
        'l': '1',  # 小写l替换为数字1
        'I': '1',  # 大写I替换为数字1
        'S': '5',  # 在数字上下文中
        'B': '8',  # 在数字上下文中
    }
    
    # 只在数字上下文中进行替换
    if re.search(r'\d', text):
        for old, new in corrections.items():
            text = text.replace(old, new)
    
    return text

def format_dimension_text(text: str) -> str:
    """格式化尺寸文本"""
    text = clean_ocr_text(text)
    
    # 标准化直径符号
    text = re.sub(r'[Øø∅⌀]', 'Ø', text)
    
    # 标准化半径符号
    text = re.sub(r'[Rr](\d)', r'R\1', text)
    
    # 标准化单位
    text = re.sub(r'(\d+(?:\.\d+)?)\s*mm', r'\1', text)
    
    return text

def extract_numeric_value(text: str) -> Optional[float]:
    """从文本中提取数值"""
    text = clean_ocr_text(text)
    
    # 匹配数字（包括小数）
    match = re.search(r'(\d+(?:\.\d+)?)', text)
    if match:
        try:
            return float(match.group(1))
        except ValueError:
            pass
    
    return None

def validate_tolerance_format(text: str) -> bool:
    """验证公差格式是否正确"""
    text = clean_ocr_text(text)
    
    # 公差格式模式
    patterns = [
        r'^[+-]\d+(\.\d+)?$',  # ±数值
        r'^±\d+(\.\d+)?$',     # ±数值
        r'^[+-]0\.\d+$',       # ±0.xxx
        r'^\+\d+(\.\d+)?/-\d+(\.\d+)?$',  # +上公差/-下公差
    ]
    
    return any(re.match(pattern, text) for pattern in patterns)

def parse_tolerance_values(text: str) -> Tuple[Optional[float], Optional[float]]:
    """解析公差值，返回(上公差, 下公差)"""
    text = clean_ocr_text(text)
    
    # 处理±格式
    if text.startswith('±'):
        value = extract_numeric_value(text[1:])
        return (value, -value if value else None)
    
    # 处理+/-格式
    match = re.match(r'^\+(\d+(?:\.\d+)?)/\-(\d+(?:\.\d+)?)$', text)
    if match:
        upper = float(match.group(1))
        lower = -float(match.group(2))
        return (upper, lower)
    
    # 处理单独的+或-
    if text.startswith('+'):
        value = extract_numeric_value(text[1:])
        return (value, None)
    elif text.startswith('-'):
        value = extract_numeric_value(text[1:])
        return (None, -value if value else None)
    
    return (None, None)

def is_dimension_text(text: str) -> bool:
    """判断是否为尺寸文本"""
    text = clean_ocr_text(text)
    
    patterns = [
        r'^\d+(\.\d+)?$',      # 纯数字
        r'^\d+(\.\d+)?mm$',    # 带mm单位
        r'^Ø\d+(\.\d+)?$',     # 直径标注
        r'^R\d+(\.\d+)?$',     # 半径标注
        r'^\d+(\.\d+)?×\d+(\.\d+)?$',  # 长×宽格式
    ]
    
    return any(re.match(pattern, text) for pattern in patterns)

def is_thread_specification(text: str) -> bool:
    """判断是否为螺纹规格"""
    text = clean_ocr_text(text)
    
    patterns = [
        r'^M\d+$',                    # M8, M10等
        r'^M\d+[×x]\d+(\.\d+)?$',     # M8×1.25等
        r'^M\d+-\d+(\.\d+)?$',        # M8-1.25等
    ]
    
    return any(re.match(pattern, text) for pattern in patterns)

def standardize_thread_text(text: str) -> str:
    """标准化螺纹文本格式"""
    text = clean_ocr_text(text)
    
    # 统一使用×符号
    text = re.sub(r'[x×]', '×', text)
    
    # 确保M后面直接跟数字
    text = re.sub(r'M\s+(\d)', r'M\1', text)
    
    return text
```

### 4.2 在MainWindow中集成文本工具

```python
# 在MainWindow顶部添加导入
from ui.utils.text_utils import (
    clean_ocr_text,
    format_dimension_text,
    extract_numeric_value,
    validate_tolerance_format,
    is_dimension_text,
    is_thread_specification
)

# 替换MainWindow中的相关方法调用
def process_ocr_text(self, text):
    """处理OCR文本"""
    cleaned_text = clean_ocr_text(text)
    
    if is_dimension_text(cleaned_text):
        return format_dimension_text(cleaned_text)
    elif is_thread_specification(cleaned_text):
        return standardize_thread_text(cleaned_text)
    
    return cleaned_text
```

## 步骤5: 创建常量配置模块 (30分钟)

### 5.1 创建文件 `ui/config/ui_constants.py`

```python
#!/usr/bin/env python3
"""
UI相关常量配置
"""

from PySide6.QtCore import QSize

# 窗口配置
WINDOW_MIN_SIZE = (1200, 800)
WINDOW_DEFAULT_SIZE = (1600, 900)
WINDOW_TITLE = "图纸标注系统"

# 面板大小配置
PANEL_SIZES = {
    'ocr_panel_max_height': 200,
    'annotation_panel_min_height': 200,
    'property_panel_min_width': 300,
    'graphics_panel_min_size': (800, 600)
}

# 按钮样式配置
BUTTON_STYLES = {
    'primary': """
        QPushButton { 
            background-color: #007bff; 
            color: white; 
            font-weight: bold; 
            border: none; 
            min-height: 25px; 
            border-radius: 4px;
        } 
        QPushButton:hover { 
            background-color: #0056b3; 
        }
        QPushButton:disabled { 
            background-color: #cccccc; 
            color: #666666; 
        }
    """,
    'secondary': """
        QPushButton { 
            background-color: #6c757d; 
            color: white; 
            border: none; 
            min-height: 25px; 
            border-radius: 4px;
        } 
        QPushButton:hover { 
            background-color: #545b62; 
        }
    """
}

# 图标配置
ICONS = {
    'open_file': "📁",
    'ocr_start': "🔍",
    'ocr_running': "🔄",
    'area_select': "📐",
    'export': "📊",
    'clear': "🗑️",
    'settings': "⚙️"
}

# 快捷键配置
SHORTCUTS = {
    'open_file': "Ctrl+O",
    'start_ocr': "Ctrl+R",
    'area_select': "Q",
    'save': "Ctrl+S",
    'export': "Ctrl+E"
}
```

### 5.2 创建文件 `ui/config/ocr_constants.py`

```python
#!/usr/bin/env python3
"""
OCR相关常量配置
"""

# OCR默认参数
OCR_DEFAULTS = {
    'confidence_threshold': 0.30,
    'cpu_threads': 8,
    'timeout_seconds': 300,
    'max_image_size': 4096,
    'min_text_height': 10
}

# OCR处理参数
OCR_PROCESSING = {
    'enhance_contrast': True,
    'denoise': True,
    'auto_rotate': True,
    'merge_adjacent_threshold': 50,
    'vertical_text_ratio': 1.2
}

# 文本分类阈值
TEXT_CLASSIFICATION = {
    'dimension_confidence': 0.8,
    'tolerance_confidence': 0.7,
    'thread_confidence': 0.9,
    'annotation_confidence': 0.6
}
```

## 步骤6: 验证和测试 (45分钟)

### 6.1 功能验证清单

```python
# 创建验证脚本 tests/verify_stage1.py
#!/usr/bin/env python3
"""
第一阶段功能验证脚本
"""

def verify_coordinate_utils():
    """验证坐标工具函数"""
    from ui.utils.coordinate_utils import *
    from PySide6.QtCore import QPointF, QRectF
    
    print("验证坐标工具函数...")
    
    # 测试距离计算
    p1, p2 = QPointF(0, 0), QPointF(3, 4)
    distance = calculate_distance_between_points(p1, p2)
    assert abs(distance - 5.0) < 0.01, f"距离计算错误: {distance}"
    
    # 测试矩形中心
    rect = QRectF(10, 20, 100, 200)
    center = get_rect_center(rect)
    assert center.x() == 60 and center.y() == 120, f"中心计算错误: {center}"
    
    print("✅ 坐标工具函数验证通过")

def verify_text_utils():
    """验证文本工具函数"""
    from ui.utils.text_utils import *
    
    print("验证文本工具函数...")
    
    # 测试文本清理
    cleaned = clean_ocr_text("  Ø25  mm  ")
    assert cleaned == "Ø25 mm", f"文本清理错误: {cleaned}"
    
    # 测试数值提取
    value = extract_numeric_value("Ø25.5")
    assert value == 25.5, f"数值提取错误: {value}"
    
    # 测试尺寸识别
    assert is_dimension_text("25.5"), "尺寸识别失败"
    assert is_thread_specification("M8×1.25"), "螺纹识别失败"
    
    print("✅ 文本工具函数验证通过")

def verify_constants():
    """验证常量配置"""
    from ui.config.ui_constants import *
    from ui.config.ocr_constants import *
    
    print("验证常量配置...")
    
    assert WINDOW_MIN_SIZE == (1200, 800), "窗口最小尺寸配置错误"
    assert OCR_DEFAULTS['confidence_threshold'] == 0.30, "OCR默认置信度配置错误"
    
    print("✅ 常量配置验证通过")

if __name__ == '__main__':
    verify_coordinate_utils()
    verify_text_utils()
    verify_constants()
    print("\n🎉 第一阶段验证全部通过！")
```

### 6.2 运行完整应用测试

```bash
# 运行应用并测试基本功能
python main.py

# 测试清单：
# 1. 应用正常启动
# 2. 界面显示正常
# 3. 文件加载功能正常
# 4. OCR识别功能正常
# 5. 标注创建功能正常
# 6. 所有按钮和菜单响应正常
```

## 完成标准

- [ ] 所有工具函数成功提取到独立模块
- [ ] 所有常量配置移到配置文件
- [ ] MainWindow中保留向后兼容的wrapper方法
- [ ] 所有现有功能100%正常工作
- [ ] 代码结构更加清晰和模块化
- [ ] 单元测试全部通过

## 下一阶段准备

完成第一阶段后，MainWindow的行数应该减少约100-150行，为第二阶段的UI组件提取奠定基础。

**预期效果**:
- 代码更加模块化
- 工具函数可复用
- 配置管理更加统一
- 为后续重构建立良好基础

这个阶段是整个重构过程的基石，确保这一阶段的质量对后续阶段的成功至关重要。
