# MainWindow 拆分实施计划 - 按难易程度排序

## 总体策略

**核心原则**: 从外围到核心，从独立到耦合，从简单到复杂
**验证标准**: 每个阶段完成后，所有现有功能必须100%正常工作

## 难易程度评估矩阵

| 模块 | 复杂度 | 耦合度 | 依赖数 | 风险级别 | 优先级 | 预估工时 |
|------|--------|--------|--------|----------|--------|----------|
| 工具函数提取 | ⭐ | ⭐ | 0 | 极低 | 1 | 4小时 |
| 常量配置提取 | ⭐ | ⭐ | 0 | 极低 | 2 | 2小时 |
| 菜单工具栏提取 | ⭐⭐ | ⭐⭐ | 1 | 低 | 3 | 6小时 |
| 状态栏管理提取 | ⭐⭐ | ⭐⭐ | 2 | 低 | 4 | 4小时 |
| UI布局构建器 | ⭐⭐⭐ | ⭐⭐ | 2 | 中低 | 5 | 8小时 |
| 文件处理控制器 | ⭐⭐⭐ | ⭐⭐⭐ | 3 | 中 | 6 | 12小时 |
| 数据服务层 | ⭐⭐⭐⭐ | ⭐⭐⭐ | 4 | 中高 | 7 | 10小时 |
| 事件处理控制器 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 5 | 高 | 8 | 8小时 |
| OCR控制器 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 6 | 高 | 9 | 16小时 |
| 标注控制器 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 7 | 极高 | 10 | 20小时 |

**总预估工时**: 90小时 (约11-12个工作日)

## 第一阶段：基础工具提取 (优先级1-2, 极低风险)

### 阶段1.1: 工具函数提取 (4小时)

**目标**: 提取独立的工具函数，零依赖，零风险

**拆分内容**:
```python
# 新建文件: ui/utils/coordinate_utils.py
def map_scene_to_view_coordinates(scene_point, transform)
def calculate_distance_between_points(p1, p2)
def normalize_rect(rect)
def get_rect_center(rect)

# 新建文件: ui/utils/text_utils.py  
def clean_ocr_text(text)
def format_dimension_text(text)
def validate_tolerance_format(text)
def extract_numeric_value(text)

# 新建文件: ui/utils/image_utils.py
def create_thumbnail(pixmap, size)
def calculate_image_scale_factor(image_size, target_size)
def get_image_info(file_path)
```

**实施步骤**:
1. 创建 `ui/utils/` 目录和 `__init__.py`
2. 从MainWindow中识别纯函数（无self依赖）
3. 复制函数到新文件，保持原函数作为wrapper
4. 逐个替换调用，测试每个函数
5. 删除MainWindow中的原函数

**验证方法**:
- 运行完整功能测试，确保所有计算结果一致
- 检查图像显示、坐标转换、文本处理功能

**回滚方案**: 简单删除新文件，恢复原函数即可

### 阶段1.2: 常量配置提取 (2小时)

**目标**: 将硬编码常量移到配置文件

**拆分内容**:
```python
# 新建文件: ui/config/ui_constants.py
WINDOW_MIN_SIZE = (1200, 800)
WINDOW_DEFAULT_SIZE = (1600, 900)
PANEL_SIZES = {...}
BUTTON_STYLES = {...}
COLOR_SCHEMES = {...}

# 新建文件: ui/config/ocr_constants.py
DEFAULT_CONFIDENCE = 0.30
DEFAULT_THREAD_COUNT = 8
OCR_TIMEOUT = 300
```

**实施步骤**:
1. 扫描MainWindow中的所有硬编码数值
2. 按功能分类创建常量文件
3. 在MainWindow中导入并替换
4. 测试所有数值相关功能

**验证方法**:
- 检查窗口大小、按钮样式、颜色显示
- 验证OCR参数设置正确

## 第二阶段：UI组件提取 (优先级3-5, 低-中低风险)

### 阶段2.1: 菜单工具栏提取 (6小时)

**目标**: 提取菜单和工具栏创建逻辑

**风险评估**: 低风险 - 主要是UI创建代码，逻辑简单

**实施步骤**:
```python
# 新建文件: ui/components/menu_toolbar.py
class MenuToolbarBuilder:
    def __init__(self, main_window):
        self.main_window = main_window
    
    def setup_menu_bar(self):
        # 从MainWindow.setup_menu_bar()移动代码
        pass
    
    def setup_toolbar(self):
        # 从MainWindow.setup_toolbar()移动代码  
        pass

# 在MainWindow.__init__()中:
self.menu_toolbar_builder = MenuToolbarBuilder(self)
self.menu_toolbar_builder.setup_menu_bar()
self.menu_toolbar_builder.setup_toolbar()
```

**注意事项**:
- 保持所有信号连接不变
- 确保快捷键功能正常
- 保持菜单项的启用/禁用状态逻辑

**验证方法**:
- 测试所有菜单项功能
- 验证快捷键响应
- 检查工具栏按钮状态

### 阶段2.2: 状态栏管理提取 (4小时)

**目标**: 提取状态栏和进度条管理

**实施步骤**:
```python
# 新建文件: ui/components/status_manager.py
class StatusManager:
    def __init__(self, main_window):
        self.main_window = main_window
        self.status_bar = main_window.statusBar()
        self.progress_bar = None
        
    def show_message(self, message, timeout=0):
        self.status_bar.showMessage(message, timeout)
        
    def show_progress(self, value):
        if self.progress_bar:
            self.progress_bar.setValue(value)
```

### 阶段2.3: UI布局构建器 (8小时)

**目标**: 提取界面布局创建逻辑

**风险评估**: 中低风险 - 涉及复杂的布局逻辑，但相对独立

**实施步骤**:
1. 创建 `UIBuilder` 类
2. 移动 `setup_ui()` 相关代码
3. 保持所有控件引用
4. 测试界面布局和响应

**关键注意事项**:
- 确保所有控件的父子关系正确
- 保持布局比例和大小设置
- 验证响应式布局功能

## 第三阶段：业务逻辑控制器 (优先级6-8, 中-高风险)

### 阶段3.1: 文件处理控制器 (12小时)

**目标**: 提取文件加载、PDF处理等功能

**风险评估**: 中风险 - 涉及文件I/O和状态管理

**拆分内容**:
```python
# 新建文件: ui/controllers/file_controller.py
class FileController(QObject):
    file_loaded = Signal(str)  # 文件路径
    page_changed = Signal(int)  # 页码
    
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        
    def open_file(self):
        # 从MainWindow.open_file()移动
        pass
        
    def load_pdf_page(self, page_index):
        # 从MainWindow.load_pdf_page()移动
        pass
```

**实施步骤**:
1. 创建FileController类
2. 移动文件相关方法（约15个方法）
3. 设置信号连接
4. 更新MainWindow中的调用
5. 测试文件加载功能

**验证方法**:
- 测试各种文件格式加载
- 验证PDF多页功能
- 检查文件状态同步

### 阶段3.2: 数据服务层 (10小时)

**目标**: 统一数据访问和状态管理

**风险评估**: 中高风险 - 涉及数据一致性

**实施步骤**:
```python
# 新建文件: ui/services/data_service.py
class DataService(QObject):
    data_changed = Signal()
    
    def __init__(self):
        super().__init__()
        self.annotations_data = {}
        self.ocr_results = []
        self.page_data = {}
        
    def add_annotation(self, annotation):
        # 统一的数据添加接口
        pass
        
    def get_annotations_by_page(self, page_index):
        # 按页获取标注数据
        pass
```

### 阶段3.3: 事件处理控制器 (8小时)

**目标**: 提取键盘、鼠标事件处理

**风险评估**: 高风险 - 涉及复杂的事件处理逻辑

**实施步骤**:
1. 创建EventController类
2. 移动事件处理方法
3. 保持事件传播机制
4. 测试所有交互功能

## 第四阶段：核心业务控制器 (优先级9-10, 高-极高风险)

### 阶段4.1: OCR控制器 (16小时)

**目标**: 提取OCR识别相关功能

**风险评估**: 高风险 - 复杂的异步处理和结果管理

**关键挑战**:
- 复杂的OCR结果合并逻辑
- 多线程处理和信号连接
- 区域OCR和全局OCR的协调

**实施步骤**:
1. 创建OCRController类
2. 移动OCR相关方法（约12个方法）
3. 重新设计信号连接
4. 测试OCR功能完整性
5. 验证结果显示和处理

**验证方法**:
- 测试全局OCR识别
- 测试区域OCR识别
- 验证OCR结果合并和分类
- 检查多线程稳定性

### 阶段4.2: 标注控制器 (20小时)

**目标**: 提取标注创建、编辑、管理功能

**风险评估**: 极高风险 - 最复杂的业务逻辑

**关键挑战**:
- 复杂的标注创建逻辑
- 标注与OCR结果的关联
- 标注排序和重排功能
- 多种标注样式管理

**实施步骤**:
1. 创建AnnotationController类
2. 移动标注相关方法（约18个方法）
3. 重新设计数据流
4. 测试标注功能完整性
5. 验证与其他模块的集成

**验证方法**:
- 测试标注创建和编辑
- 验证标注排序功能
- 检查标注样式管理
- 测试标注导出功能

## 并行开发策略

### 可并行开发的模块组合:

**组合1**: 工具函数 + 常量配置 (可同时进行)
**组合2**: 菜单工具栏 + 状态栏管理 (UI组件，可并行)
**组合3**: 数据服务 + 事件控制器 (相对独立)

### 必须串行的依赖关系:

```
工具函数 → UI布局构建器 → 文件控制器 → OCR控制器 → 标注控制器
```

## 风险控制和回滚方案

### 每阶段的安全措施:

1. **代码备份**: 每个阶段开始前创建Git分支
2. **渐进替换**: 保留原方法作为wrapper，逐步替换
3. **功能验证**: 每个模块完成后运行完整测试套件
4. **性能监控**: 确保重构不影响性能

### 回滚策略:

**Level 1 (工具函数)**: 删除新文件，恢复原函数
**Level 2 (UI组件)**: 恢复原UI创建代码
**Level 3 (控制器)**: 回滚到上一个稳定版本
**Level 4 (核心控制器)**: 完整回滚到重构前状态

## 验证检查清单

### 每阶段完成后必须验证:

**功能完整性**:
- [ ] 所有菜单项功能正常
- [ ] 所有快捷键响应正确
- [ ] 文件加载功能完整
- [ ] OCR识别功能正常
- [ ] 标注创建编辑正常
- [ ] 导出功能完整

**性能稳定性**:
- [ ] 启动时间无明显增加
- [ ] 内存使用无异常增长
- [ ] OCR处理速度保持
- [ ] UI响应速度正常

**用户体验**:
- [ ] 界面外观完全一致
- [ ] 交互行为无变化
- [ ] 错误处理机制正常
- [ ] 状态提示准确

## 实施时间表

| 阶段 | 模块 | 预估时间 | 累计时间 | 里程碑 |
|------|------|----------|----------|--------|
| 1.1 | 工具函数提取 | 4小时 | 4小时 | 基础工具完成 |
| 1.2 | 常量配置提取 | 2小时 | 6小时 | 配置分离完成 |
| 2.1 | 菜单工具栏提取 | 6小时 | 12小时 | UI组件开始 |
| 2.2 | 状态栏管理提取 | 4小时 | 16小时 | 状态管理完成 |
| 2.3 | UI布局构建器 | 8小时 | 24小时 | UI重构完成 |
| 3.1 | 文件处理控制器 | 12小时 | 36小时 | 文件处理分离 |
| 3.2 | 数据服务层 | 10小时 | 46小时 | 数据层完成 |
| 3.3 | 事件处理控制器 | 8小时 | 54小时 | 事件处理分离 |
| 4.1 | OCR控制器 | 16小时 | 70小时 | OCR模块完成 |
| 4.2 | 标注控制器 | 20小时 | 90小时 | 重构全部完成 |

**建议实施节奏**: 每天6-8小时，总计11-15个工作日完成

这个实施计划确保了重构过程的安全性和可控性，通过从简单到复杂的渐进式重构，最大程度降低了风险，同时保证了功能的完整性。
