# PyQt-Bubble 技术架构图

## 系统架构总览

```mermaid
graph TB
    subgraph "应用层 (Application Layer)"
        A[main.py - 应用入口]
        B[run.py - 启动脚本]
    end
    
    subgraph "界面层 (UI Layer)"
        C[MainWindow - 主窗口]
        D[GraphicsView - 图形视图]
        E[AnnotationTable - 标注列表]
        F[PropertyEditor - 属性编辑]
        G[ExportManager - 导出管理]
        H[FileManager - 文件管理]
    end
    
    subgraph "核心业务层 (Core Layer)"
        I[PaddleOCRWorker - OCR处理]
        J[BubbleAnnotationItem - 标注组件]
        K[FileLoader - 文件加载]
        L[AnnotationDataModel - 数据模型]
    end
    
    subgraph "工具层 (Utils Layer)"
        M[constants.py - 常量配置]
        N[dependencies.py - 依赖管理]
        O[StateManager - 状态管理]
    end
    
    subgraph "工作线程层 (Workers Layer)"
        P[PDFLoaderWorker - PDF加载]
        Q[FileLoaderWorker - 文件加载]
        R[OCR处理线程池]
    end
    
    subgraph "外部依赖 (External Dependencies)"
        S[PaddleOCR - OCR引擎]
        T[PySide6 - Qt框架]
        U[OpenCV - 图像处理]
        V[PyMuPDF - PDF处理]
        W[NumPy - 数值计算]
    end
    
    A --> C
    B --> A
    C --> D
    C --> E
    C --> F
    C --> G
    C --> H
    C --> I
    C --> J
    C --> K
    D --> J
    I --> S
    K --> V
    K --> U
    P --> V
    Q --> K
    C --> P
    C --> Q
    I --> R
    C --> L
    C --> O
    M --> C
    N --> C
    
    classDef appLayer fill:#e1f5fe
    classDef uiLayer fill:#f3e5f5
    classDef coreLayer fill:#e8f5e8
    classDef utilsLayer fill:#fff3e0
    classDef workersLayer fill:#fce4ec
    classDef externalLayer fill:#f1f8e9
    
    class A,B appLayer
    class C,D,E,F,G,H uiLayer
    class I,J,K,L coreLayer
    class M,N,O utilsLayer
    class P,Q,R workersLayer
    class S,T,U,V,W externalLayer
```

## 数据流架构

```mermaid
sequenceDiagram
    participant User as 用户
    participant MW as MainWindow
    participant GV as GraphicsView
    participant FL as FileLoader
    participant OCR as PaddleOCRWorker
    participant AI as AnnotationItem
    participant DM as DataModel
    
    User->>MW: 打开文件
    MW->>FL: 加载文件
    FL->>MW: 返回图像数据
    MW->>GV: 显示图像
    
    User->>GV: 选择OCR区域
    GV->>MW: 区域选择完成
    MW->>OCR: 启动OCR识别
    OCR->>OCR: 图像预处理
    OCR->>OCR: 文本检测
    OCR->>OCR: 文本识别
    OCR->>OCR: 文本分类
    OCR->>MW: 返回识别结果
    
    MW->>AI: 创建标注项
    AI->>GV: 添加到场景
    MW->>DM: 更新数据模型
    
    User->>AI: 编辑标注
    AI->>MW: 发送更新信号
    MW->>DM: 同步数据
```

## 模块依赖关系

```mermaid
graph LR
    subgraph "核心模块"
        A[main_window.py<br/>3157行]
        B[paddle_ocr_worker.py<br/>664行]
        C[annotation_item.py<br/>611行]
        D[file_loader.py<br/>618行]
        E[graphics_view.py<br/>629行]
    end
    
    subgraph "工具模块"
        F[constants.py<br/>267行]
        G[dependencies.py<br/>100行]
    end
    
    subgraph "界面组件"
        H[annotation_list.py]
        I[property_editor.py]
    end
    
    subgraph "管理器"
        J[export_manager.py<br/>475行]
        K[file_manager.py]
    end
    
    subgraph "工作线程"
        L[pdf_loader_worker.py<br/>82行]
        M[file_loader_worker.py]
    end
    
    A --> B
    A --> C
    A --> D
    A --> E
    A --> H
    A --> I
    A --> J
    A --> K
    A --> L
    A --> M
    A --> F
    A --> G
    
    B --> F
    C --> F
    D --> F
    E --> F
    
    L --> D
    M --> D
```

## 关键技术栈

| 层级 | 技术组件 | 版本 | 作用 |
|------|----------|------|------|
| **界面框架** | PySide6 | >=6.0.0 | Qt界面框架 |
| **OCR引擎** | PaddleOCR | 2.9.0 | 文字识别 |
| **图像处理** | OpenCV | 4.11.0.86 | 计算机视觉 |
| **PDF处理** | PyMuPDF | 1.26.0 | PDF渲染 |
| **数值计算** | NumPy | 1.25.2 | 数组操作 |
| **图像库** | Pillow | 11.2.1 | 图像处理 |
| **深度学习** | PaddlePaddle | 2.6.1 | OCR后端 |

## 性能特性

### 内存管理
- **图像内存限制**: 2048MB
- **分块加载**: 大图像自动降采样
- **缓存策略**: 模型预加载和结果缓存
- **垃圾回收**: 及时释放临时资源

### 并发处理
- **多线程OCR**: QThreadPool管理
- **异步文件加载**: 后台加载大文件
- **信号-槽通信**: 线程安全的消息传递
- **响应式UI**: 非阻塞用户界面

### 渲染优化
- **设备坐标缓存**: QGraphicsObject缓存
- **抗锯齿渲染**: QPainter.Antialiasing
- **视口裁剪**: 只渲染可见区域
- **LOD支持**: 多级细节显示

## 扩展架构

### 插件化设计
```python
class PluginManager:
    def __init__(self):
        self.ocr_plugins = {}      # OCR引擎插件
        self.export_plugins = {}   # 导出格式插件
        self.annotation_plugins = {} # 标注类型插件
    
    def register_ocr_plugin(self, name: str, plugin_class):
        """注册OCR插件"""
        self.ocr_plugins[name] = plugin_class
    
    def register_export_plugin(self, format: str, plugin_class):
        """注册导出插件"""
        self.export_plugins[format] = plugin_class
```

### 配置管理
```python
class ConfigManager:
    def __init__(self):
        self.user_config = {}      # 用户配置
        self.system_config = {}    # 系统配置
        self.theme_config = {}     # 主题配置
    
    def load_config(self, config_path: str):
        """加载配置文件"""
        pass
    
    def save_config(self, config_path: str):
        """保存配置文件"""
        pass
```

## 质量保证

### 错误处理层级
1. **输入验证**: 文件格式、参数范围检查
2. **异常捕获**: try-catch包装关键操作
3. **优雅降级**: GPU失败自动切换CPU
4. **用户反馈**: 详细的错误信息提示

### 日志系统
```python
# 分级日志记录
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('debug.log', 'w', 'utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
```

### 性能监控
- **OCR性能日志**: ocr_performance.log
- **内存使用监控**: psutil集成
- **响应时间统计**: 关键操作计时
- **错误率统计**: 异常频率跟踪

## 部署架构

### 打包配置
- **PyInstaller**: 单文件可执行程序
- **模型文件**: 本地models目录
- **依赖库**: 自动打包所需DLL
- **配置文件**: 用户配置持久化

### 系统要求
- **操作系统**: Windows 10+ / Linux / macOS
- **Python版本**: 3.10+
- **内存要求**: 4GB+ (推荐8GB)
- **GPU支持**: CUDA 11.2+ (可选)
- **磁盘空间**: 2GB+ (包含模型文件)

这个技术架构展现了PyQt-Bubble项目的完整技术栈和设计思路，为后续的开发、维护和扩展提供了清晰的指导。
