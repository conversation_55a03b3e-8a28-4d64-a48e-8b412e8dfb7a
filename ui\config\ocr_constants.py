#!/usr/bin/env python3
"""
OCR相关常量配置
"""

# OCR默认参数
OCR_DEFAULTS = {
    'confidence_threshold': 0.30,
    'cpu_threads': 8,
    'timeout_seconds': 300,
    'max_image_size': 4096,
    'min_text_height': 10,
    'language': 'ch_sim',
    'use_gpu': False,
    'enhance_contrast': True,
    'denoise': True
}

# OCR处理参数
OCR_PROCESSING = {
    'enhance_contrast': True,
    'denoise': True,
    'auto_rotate': True,
    'merge_adjacent_threshold': 50,
    'vertical_text_ratio': 1.2,
    'min_selection_area': 10,
    'max_memory_limit': 2048,  # MB
    'image_quality_threshold': 0.8
}

# 文本分类阈值
TEXT_CLASSIFICATION = {
    'dimension_confidence': 0.8,
    'tolerance_confidence': 0.7,
    'thread_confidence': 0.9,
    'annotation_confidence': 0.6,
    'min_text_length': 1,
    'max_text_length': 100
}

# OCR结果合并参数
MERGE_PARAMETERS = {
    'distance_threshold': 50,      # 像素
    'angle_threshold': 15,         # 度
    'size_ratio_threshold': 2.0,   # 倍数
    'overlap_threshold': 0.1,      # 重叠比例
    'line_height_ratio': 1.5       # 行高比例
}

# 区域OCR参数
AREA_OCR = {
    'min_area_size': 20,           # 最小区域尺寸
    'max_area_size': 2048,         # 最大区域尺寸
    'aspect_ratio_threshold': 1.2, # 竖排文本判断阈值
    'crop_padding': 5,             # 裁剪填充
    'temp_file_prefix': 'area_ocr_',
    'temp_file_suffix': '.png'
}

# OCR性能参数
PERFORMANCE = {
    'max_concurrent_workers': 4,   # 最大并发工作器数
    'worker_timeout': 60,          # 工作器超时时间（秒）
    'memory_limit_per_worker': 512, # 每个工作器内存限制（MB）
    'gpu_memory_limit': 500,       # GPU内存限制（MB）
    'cpu_thread_limit': 32         # CPU线程数限制
}

# 文本类型识别模式
TEXT_TYPE_PATTERNS = {
    'dimension': [
        r'^\d+(\.\d+)?$',           # 纯数字
        r'^\d+(\.\d+)?mm$',         # 带mm单位
        r'^Ø\d+(\.\d+)?$',          # 直径标注
        r'^R\d+(\.\d+)?$',          # 半径标注
        r'^Φ\d+(\.\d+)?$',          # Φ直径标注
        r'^\d+(\.\d+)?×\d+(\.\d+)?$' # 长×宽格式
    ],
    'tolerance': [
        r'^[+-]\d+(\.\d+)?$',       # ±数值
        r'^±\d+(\.\d+)?$',          # ±数值
        r'^[+-]0\.\d+$',            # ±0.xxx
        r'^0$'                      # 单独的0
    ],
    'thread': [
        r'^M\d+$',                  # M8, M10等
        r'^M\d+[×x]\d+(\.\d+)?$',   # M8×1.25等
        r'^M\d+-\d+(\.\d+)?$'       # M8-1.25等
    ],
    'surface': [
        r'^Ra\d+(\.\d+)?$',         # 表面粗糙度
        r'^Rz\d+(\.\d+)?$',         # 表面粗糙度
        r'^▽+$',                    # 表面符号
        r'^\d+(\.\d+)?μm$'          # 微米单位
    ],
    'material': [
        r'^Q\d+$',                  # 钢材牌号
        r'^\d+Cr\d+$',              # 合金钢
        r'^SUS\d+$',                # 不锈钢
        r'^Al\d+$'                  # 铝合金
    ]
}

# OCR错误修正规则
ERROR_CORRECTION = {
    'character_replacements': {
        'O': '0',   # 字母O替换为数字0
        'l': '1',   # 小写l替换为数字1
        'I': '1',   # 大写I替换为数字1
        'S': '5',   # 在数字上下文中
        'B': '8',   # 在数字上下文中
        'G': '6',   # 在数字上下文中
        'Z': '2'    # 在数字上下文中
    },
    'symbol_standardization': {
        'Ø': 'Φ',   # 统一直径符号
        '∅': 'Φ',   # 统一直径符号
        '⌀': 'Φ',   # 统一直径符号
        '×': 'x',   # 统一乘号
        'X': 'x'    # 统一乘号
    },
    'unit_normalization': {
        'mm': '',   # 移除mm单位
        'MM': '',   # 移除MM单位
        'μm': 'um', # 标准化微米
        'µm': 'um'  # 标准化微米
    }
}

# OCR质量评估参数
QUALITY_ASSESSMENT = {
    'min_confidence': 0.1,         # 最小置信度
    'blur_threshold': 100,         # 模糊度阈值
    'contrast_threshold': 50,      # 对比度阈值
    'noise_threshold': 0.1,        # 噪声阈值
    'resolution_threshold': 72     # 分辨率阈值（DPI）
}

# 调试和日志配置
DEBUG_CONFIG = {
    'save_intermediate_images': False,  # 保存中间图像
    'log_ocr_results': True,           # 记录OCR结果
    'log_performance': True,           # 记录性能数据
    'debug_output_dir': 'debug_ocr',   # 调试输出目录
    'max_log_files': 10                # 最大日志文件数
}

# 缓存配置
CACHE_CONFIG = {
    'enable_result_cache': True,       # 启用结果缓存
    'cache_size_limit': 100,           # 缓存条目限制
    'cache_ttl': 3600,                 # 缓存生存时间（秒）
    'cache_dir': 'cache/ocr',          # 缓存目录
    'auto_cleanup': True               # 自动清理过期缓存
}
