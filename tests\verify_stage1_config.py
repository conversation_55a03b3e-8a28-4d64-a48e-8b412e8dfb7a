#!/usr/bin/env python3
"""
第一阶段配置常量验证脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def verify_ui_constants():
    """验证UI常量配置"""
    print("🔍 验证UI常量配置...")
    
    try:
        from ui.config.ui_constants import (
            WINDOW_MIN_SIZE,
            WINDOW_DEFAULT_SIZE,
            WINDOW_TITLE,
            PANEL_SIZES,
            BUTTON_STYLES,
            ICONS,
            SHORTCUTS,
            COLORS
        )
        
        # 验证窗口配置
        assert WINDOW_MIN_SIZE == (1200, 800), f"窗口最小尺寸配置错误: {WINDOW_MIN_SIZE}"
        assert WINDOW_DEFAULT_SIZE == (1600, 900), f"窗口默认尺寸配置错误: {WINDOW_DEFAULT_SIZE}"
        assert WINDOW_TITLE == "图纸标注系统", f"窗口标题配置错误: {WINDOW_TITLE}"
        print("  ✅ 窗口配置正确")
        
        # 验证面板配置
        assert 'ocr_panel_max_height' in PANEL_SIZES, "缺少OCR面板高度配置"
        assert PANEL_SIZES['ocr_panel_max_height'] == 200, "OCR面板高度配置错误"
        print("  ✅ 面板配置正确")
        
        # 验证按钮样式
        assert 'primary' in BUTTON_STYLES, "缺少主要按钮样式"
        assert 'background-color' in BUTTON_STYLES['primary'], "按钮样式格式错误"
        print("  ✅ 按钮样式配置正确")
        
        # 验证图标配置
        assert 'ocr_start' in ICONS, "缺少OCR开始图标"
        assert 'ocr_unavailable' in ICONS, "缺少OCR不可用图标"
        assert ICONS['ocr_start'] == "🔍", "OCR开始图标错误"
        print("  ✅ 图标配置正确")
        
        # 验证快捷键配置
        assert 'start_ocr' in SHORTCUTS, "缺少OCR快捷键配置"
        assert 'area_select' in SHORTCUTS, "缺少区域选择快捷键配置"
        assert SHORTCUTS['area_select'] == "Q", "区域选择快捷键配置错误"
        print("  ✅ 快捷键配置正确")
        
        # 验证颜色配置
        assert 'primary' in COLORS, "缺少主要颜色配置"
        assert 'background' in COLORS, "缺少背景颜色配置"
        print("  ✅ 颜色配置正确")
        
        print("✅ UI常量配置验证通过")
        return True
        
    except Exception as e:
        print(f"❌ UI常量配置验证失败: {e}")
        return False

def verify_ocr_constants():
    """验证OCR常量配置"""
    print("🔍 验证OCR常量配置...")
    
    try:
        from ui.config.ocr_constants import (
            OCR_DEFAULTS,
            OCR_PROCESSING,
            TEXT_CLASSIFICATION,
            AREA_OCR,
            ERROR_CORRECTION
        )
        
        # 验证OCR默认参数
        assert 'confidence_threshold' in OCR_DEFAULTS, "缺少置信度阈值配置"
        assert 'cpu_threads' in OCR_DEFAULTS, "缺少CPU线程数配置"
        assert OCR_DEFAULTS['confidence_threshold'] == 0.30, "置信度阈值配置错误"
        assert OCR_DEFAULTS['cpu_threads'] == 8, "CPU线程数配置错误"
        print("  ✅ OCR默认参数正确")
        
        # 验证OCR处理参数
        assert 'enhance_contrast' in OCR_PROCESSING, "缺少对比度增强配置"
        assert 'denoise' in OCR_PROCESSING, "缺少降噪配置"
        assert 'vertical_text_ratio' in OCR_PROCESSING, "缺少竖排文本比例配置"
        assert OCR_PROCESSING['vertical_text_ratio'] == 1.2, "竖排文本比例配置错误"
        print("  ✅ OCR处理参数正确")
        
        # 验证文本分类配置
        assert 'dimension_confidence' in TEXT_CLASSIFICATION, "缺少尺寸置信度配置"
        assert 'tolerance_confidence' in TEXT_CLASSIFICATION, "缺少公差置信度配置"
        print("  ✅ 文本分类配置正确")
        
        # 验证区域OCR配置
        assert 'min_area_size' in AREA_OCR, "缺少最小区域尺寸配置"
        assert 'aspect_ratio_threshold' in AREA_OCR, "缺少宽高比阈值配置"
        assert AREA_OCR['aspect_ratio_threshold'] == 1.2, "宽高比阈值配置错误"
        print("  ✅ 区域OCR配置正确")
        
        # 验证错误修正配置
        assert 'character_replacements' in ERROR_CORRECTION, "缺少字符替换配置"
        assert 'symbol_standardization' in ERROR_CORRECTION, "缺少符号标准化配置"
        assert ERROR_CORRECTION['character_replacements']['O'] == '0', "字符替换配置错误"
        print("  ✅ 错误修正配置正确")
        
        print("✅ OCR常量配置验证通过")
        return True
        
    except Exception as e:
        print(f"❌ OCR常量配置验证失败: {e}")
        return False

def verify_config_integration():
    """验证配置集成"""
    print("🔍 验证配置集成...")
    
    try:
        # 测试配置包导入
        from ui.config import (
            WINDOW_MIN_SIZE,
            OCR_DEFAULTS,
            BUTTON_STYLES
        )
        
        # 验证配置可以正常导入和使用
        assert WINDOW_MIN_SIZE is not None, "窗口尺寸配置导入失败"
        assert OCR_DEFAULTS is not None, "OCR默认配置导入失败"
        assert BUTTON_STYLES is not None, "按钮样式配置导入失败"
        print("  ✅ 配置包导入成功")
        
        # 测试MainWindow中的配置使用
        from ui.main_window import MainWindow
        print("  ✅ MainWindow配置集成成功")
        
        print("✅ 配置集成验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置集成验证失败: {e}")
        return False

def verify_config_consistency():
    """验证配置一致性"""
    print("🔍 验证配置一致性...")
    
    try:
        from ui.config.ui_constants import PANEL_SIZES, WIDGET_SIZES
        from ui.config.ocr_constants import OCR_DEFAULTS, AREA_OCR
        
        # 验证相关配置的一致性
        assert PANEL_SIZES['ocr_panel_max_height'] > 0, "OCR面板高度必须为正数"
        assert WIDGET_SIZES['button_min_height'] > 0, "按钮最小高度必须为正数"
        assert 0 < OCR_DEFAULTS['confidence_threshold'] < 1, "置信度阈值必须在0-1之间"
        assert OCR_DEFAULTS['cpu_threads'] > 0, "CPU线程数必须为正数"
        assert AREA_OCR['aspect_ratio_threshold'] > 0, "宽高比阈值必须为正数"
        print("  ✅ 数值范围检查通过")
        
        # 验证配置类型
        assert isinstance(PANEL_SIZES, dict), "面板尺寸配置必须是字典"
        assert isinstance(OCR_DEFAULTS, dict), "OCR默认配置必须是字典"
        print("  ✅ 配置类型检查通过")
        
        print("✅ 配置一致性验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置一致性验证失败: {e}")
        return False

def main():
    """主验证函数"""
    print("=" * 50)
    print("🚀 开始第一阶段验证：常量配置提取")
    print("=" * 50)
    
    results = []
    
    # 验证各个配置模块
    results.append(verify_ui_constants())
    results.append(verify_ocr_constants())
    results.append(verify_config_integration())
    results.append(verify_config_consistency())
    
    print("\n" + "=" * 50)
    print("📊 验证结果汇总")
    print("=" * 50)
    
    success_count = sum(results)
    total_count = len(results)
    
    if success_count == total_count:
        print(f"🎉 全部验证通过！({success_count}/{total_count})")
        print("✅ 第一阶段常量配置提取成功完成")
        print("📝 硬编码常量已移到配置文件，便于维护和修改")
        return True
    else:
        print(f"⚠️ 部分验证失败 ({success_count}/{total_count})")
        print("❌ 需要修复问题后再继续")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
