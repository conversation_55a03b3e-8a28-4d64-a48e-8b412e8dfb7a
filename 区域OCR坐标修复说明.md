# 区域OCR识别坐标显示错误修复说明

## 问题描述

在使用区域OCR识别功能时，发现当选择区域的高比宽大时（即竖排文本），蓝色的文本识别矩形框位置显示错误，而横排文本（宽比高大）时显示正常。

### 问题原因分析

1. **竖排文本判断逻辑**: 当选择区域的高宽比 > 1.2 时，系统判定为竖排文本
2. **图像旋转处理**: 对于竖排文本，OCR处理器会将图像旋转90度顺时针进行识别
3. **坐标变换错误**: 在将旋转后的识别结果坐标转换回原始坐标系时，变换公式有误

### 旋转坐标变换原理

当图像旋转90度顺时针时：
- 原始图像尺寸: `width × height`
- 旋转后图像尺寸: `height × width`
- 坐标变换: `(x, y) → (y, width - x)`
- 逆变换: `(x', y') → (width - y', x')`

## 修复方案

### 修复前的错误代码

```python
# 错误的逆变换公式
original_x = offset_x + (rect_height - point[1])  # ❌ 使用了rect_height
original_y = offset_y + point[0]
```

### 修复后的正确代码

```python
# 正确的逆变换公式
original_x = offset_x + (rect_width - point[1])   # ✅ 使用rect_width
original_y = offset_y + point[0]
```

### 修复的具体内容

#### 1. 边界框坐标变换修复

**文件**: `ui/main_window.py`
**位置**: `on_area_ocr_finished` 方法中的坐标调整部分

```python
for point in result['bbox']:
    if is_vertical:
        # 修复后的逆变换公式
        original_x = offset_x + (rect_width - point[1])
        original_y = offset_y + point[0]
        adjusted_bbox.append([original_x, original_y])
    else:
        # 正常情况下只添加偏移量
        adjusted_bbox.append([point[0] + offset_x, point[1] + offset_y])
```

#### 2. 中心点坐标变换修复

```python
if is_vertical:
    # 使用相同的逆变换公式
    original_center_x = offset_x + (rect_width - result['center_y'])
    original_center_y = offset_y + result['center_x']
    result['center_x'] = original_center_x
    result['center_y'] = original_center_y
```

#### 3. 边界框尺寸交换

```python
# 对于竖排文本，还需要交换bbox_width和bbox_height
if is_vertical and 'bbox_width' in result and 'bbox_height' in result:
    # 竖排文本旋转后，宽高需要交换
    original_width = result['bbox_height']
    original_height = result['bbox_width']
    result['bbox_width'] = original_width
    result['bbox_height'] = original_height
```

## 测试验证

### 测试场景

- **选择区域**: 宽度=50, 高度=150 (高宽比=3.0 > 1.2，判定为竖排)
- **区域偏移**: x=100, y=200
- **模拟OCR结果**: 在旋转后图像中的边界框和中心点

### 测试结果

```
修复前:
  边界框范围: x=[225, 245], y=[210, 240]
  期望范围: x=[100, 150], y=[200, 350]
  X坐标范围正确: ❌

修复后:
  边界框范围: x=[125, 145], y=[210, 240]
  期望范围: x=[100, 150], y=[200, 350]
  X坐标范围正确: ✅
  Y坐标范围正确: ✅
```

## 影响范围

### 修复的功能
- ✅ 竖排文本区域OCR的识别框位置显示
- ✅ 竖排文本的标注创建位置
- ✅ 竖排文本的边界框尺寸显示

### 不受影响的功能
- ✅ 横排文本区域OCR（宽>高的情况）
- ✅ 全局OCR识别
- ✅ 其他标注功能

## 使用建议

1. **测试步骤**:
   - 打开一个包含竖排文本的图纸
   - 按"Q"键激活区域OCR模式
   - 选择一个高>宽的区域进行识别
   - 验证蓝色识别框是否正确显示在选择区域内

2. **预期效果**:
   - 竖排文本的识别框应该准确显示在选择区域内
   - 创建的标注位置应该正确
   - 边界框的宽高比应该符合竖排文本特征

## 技术细节

### 坐标系变换数学原理

对于90度顺时针旋转：
```
设原始图像尺寸为 W × H
旋转后图像尺寸为 H × W

正变换: (x, y) → (y, W - x)
逆变换: (x', y') → (W - y', x')
```

### 关键修复点

1. **正确使用原始宽度**: 在逆变换中使用 `rect_width` 而不是 `rect_height`
2. **宽高交换**: 旋转后的边界框尺寸需要交换宽高
3. **一致性**: 边界框坐标和中心点坐标使用相同的变换公式

这个修复确保了区域OCR功能在处理竖排文本时能够正确显示识别结果的位置。
