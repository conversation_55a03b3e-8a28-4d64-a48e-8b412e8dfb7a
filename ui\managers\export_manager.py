#!/usr/bin/env python3
"""
导出管理模块
负责处理各种导出功能：Excel导出、模板导出、图片导出
"""

import os
import shutil
from pathlib import Path
from PySide6.QtWidgets import QMessageBox, QFileDialog

# 检查Excel支持
try:
    import openpyxl
    from openpyxl.styles import Font, Alignment
    import openpyxl.utils
    HAS_EXCEL_SUPPORT = True
except ImportError:
    HAS_EXCEL_SUPPORT = False

class ExportManager:
    """导出管理器类"""
    
    def __init__(self, main_window):
        """
        初始化导出管理器
        
        Args:
            main_window: 主窗口实例，用于访问UI组件和数据
        """
        self.main_window = main_window
    
    def export_to_excel(self):
        """导出标注列表到Excel文件"""
        if not HAS_EXCEL_SUPPORT:
            QMessageBox.warning(
                self.main_window, 
                "功能缺失", 
                "缺少 'openpyxl' 库，无法导出Excel。\n请运行: pip install openpyxl"
            )
            return
            
        if not self.main_window.annotations:
            QMessageBox.information(self.main_window, "提示", "标注列表为空，无需导出。")
            return
        
        # 构造默认文件名 - 优先使用原始PDF文件名
        if self.main_window.pdf_file_path:
            # PDF模式下使用原始PDF文件名
            pdf_name = Path(self.main_window.pdf_file_path).stem
            default_filename = f"{pdf_name}_标注列表.xlsx"
        elif self.main_window.current_file_path:
            # 普通图片模式使用当前文件名
            default_filename = f"{Path(self.main_window.current_file_path).stem}_标注列表.xlsx"
        else:
            default_filename = "标注列表.xlsx"
            
        file_path, _ = QFileDialog.getSaveFileName(
            self.main_window, 
            "导出为Excel文件", 
            default_filename, 
            "Excel 文件 (*.xlsx)"
        )
        
        if not file_path:
            return
        
        try:
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "标注数据"
            
            # 获取表头
            headers = [
                self.main_window.annotation_table.horizontalHeaderItem(i).text() 
                for i in range(self.main_window.annotation_table.columnCount())
            ]
            ws.append(headers)
            
            # 设置表头样式
            header_font = Font(bold=True)
            for cell in ws[1]: 
                cell.font = header_font
                cell.alignment = Alignment(horizontal='center', vertical='center')
            
            # 添加数据行
            for ann in self.main_window.annotations:
                row_data = [
                    str(ann.annotation_id),
                    ann.dimension_type,
                    ann.dimension,
                    ann.upper_tolerance,
                    ann.lower_tolerance,
                    "是" if ann.is_audited else "否"
                ]
                ws.append(row_data)

            # 自动调整列宽
            for col_idx, column in enumerate(ws.columns):
                max_length = 0
                column_letter = openpyxl.utils.get_column_letter(col_idx + 1)
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = max(max_length + 2, len(headers[col_idx]) + 2)
                ws.column_dimensions[column_letter].width = adjusted_width if adjusted_width < 50 else 50
            ws.column_dimensions[openpyxl.utils.get_column_letter(6)].width = 10

            wb.save(file_path)
            QMessageBox.information(
                self.main_window, 
                "导出成功", 
                f"标注列表已成功导出到:\n{file_path}"
            )
            self.main_window.status_bar.showMessage(f"成功导出到 {Path(file_path).name}", 5000)
            
        except Exception as e:
            QMessageBox.critical(
                self.main_window, 
                "导出失败", 
                f"导出到Excel时发生错误:\n{e}"
            )
            self.main_window.status_bar.showMessage("导出失败", 3000)

    def export_to_template(self):
        """将标注列表导出到Excel模板中
        - 将序号、类型、尺寸、上公差、下公差插入到A-E列
        - 从第14行开始插入
        """
        try:
            import xlwings as xw
            HAS_XLWINGS_SUPPORT = True
        except ImportError:
            HAS_XLWINGS_SUPPORT = False
            if not HAS_EXCEL_SUPPORT:
                QMessageBox.warning(
                    self.main_window,
                    "功能缺失",
                    "缺少Excel支持库。\n请运行: pip install xlwings 或 pip install openpyxl"
                )
                return

        if not self.main_window.annotations:
            QMessageBox.information(self.main_window, "提示", "标注列表为空，无需导出。")
            return

        # 直接使用项目根目录下的muban.xlsx
        # 获取项目根目录路径（从ui/managers/export_manager.py向上两级）
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        template_path = os.path.join(project_root, "muban.xlsx")

        if not os.path.exists(template_path):
            QMessageBox.critical(
                self.main_window,
                "模板文件缺失",
                f"找不到检验报告模板文件:\n{template_path}\n\n请确保项目根目录下存在 muban.xlsx 文件。"
            )
            return

        # 选择保存位置 - 构造默认文件名，优先使用原始PDF文件名
        if self.main_window.pdf_file_path:
            # PDF模式下使用原始PDF文件名
            pdf_name = Path(self.main_window.pdf_file_path).stem
            default_filename = f"{pdf_name}_检验报告.xlsx"
        elif self.main_window.current_file_path:
            # 普通图片模式使用当前文件名
            default_filename = f"{Path(self.main_window.current_file_path).stem}_检验报告.xlsx"
        else:
            default_filename = "检验报告.xlsx"

        save_path, _ = QFileDialog.getSaveFileName(
            self.main_window,
            "保存Excel文件",
            default_filename,
            "Excel 文件 (*.xlsx)"
        )
        if not save_path:
            return

        try:
            # 获取要插入的标注数据
            annotations_data = []
            for ann in self.main_window.annotations:
                row_data = [
                    str(ann.annotation_id),
                    ann.dimension_type,
                    ann.dimension,
                    ann.upper_tolerance,
                    ann.lower_tolerance
                ]
                annotations_data.append(row_data)

            # 排序标注（按ID排序）
            annotations_data.sort(key=lambda x: int(x[0]) if x[0].isdigit() else float('inf'))

            # 确定插入行的范围
            start_row = 14  # 从第14行开始
            insert_count = len(annotations_data)  # 需要插入的行数

            if HAS_XLWINGS_SUPPORT:
                # 使用xlwings插入行 (这种方式会更接近Excel手动操作)
                try:
                    # 先复制模板到保存位置
                    shutil.copy2(template_path, save_path)

                    # 用xlwings打开文件
                    app = xw.App(visible=False)
                    wb = app.books.open(save_path)
                    ws = wb.sheets[0]

                    # 插入行 - 这会像Excel手动操作一样插入干净的行
                    # 注意xlwings中行号从1开始计数
                    # 限制只在A-P列插入，不影响Q-S列
                    ws.range(f"A{start_row}:P{start_row+insert_count-1}").insert('down')

                    # 填充数据 (可选)
                    for i, row_data in enumerate(annotations_data):
                        row_idx = start_row + i
                        # 只处理A-E列
                        for j, value in enumerate(row_data):
                            col_letter = chr(65 + j)  # A=65, B=66, ...
                            ws.range(f"{col_letter}{row_idx}").value = value

                        # 计算并填充O列和P列
                        self._calculate_and_fill_columns(ws, row_idx, row_data)

                    # 保存文件并关闭Excel
                    wb.save()
                    wb.close()
                    app.quit()

                    QMessageBox.information(
                        self.main_window,
                        "导出成功",
                        f"标注列表已成功导出到:\n{save_path}"
                    )
                    self.main_window.status_bar.showMessage(f"成功导出到 {Path(save_path).name}", 5000)
                    return

                except Exception as e:
                    # 如果xlwings出错，回退到openpyxl
                    QMessageBox.warning(
                        self.main_window,
                        "提示",
                        f"使用xlwings导出失败 ({str(e)})，将尝试使用openpyxl。"
                    )

            # 如果没有xlwings支持或xlwings失败，使用openpyxl
            self._export_with_openpyxl(template_path, save_path, annotations_data, start_row, insert_count)

        except Exception as e:
            QMessageBox.critical(
                self.main_window,
                "导出失败",
                f"导出到Excel模板时发生错误:\n{e}"
            )
            self.main_window.status_bar.showMessage("导出失败", 3000)

    def _calculate_and_fill_columns(self, ws, row_idx, row_data):
        """计算并填充O列和P列的数据"""
        dimension = row_data[2] if len(row_data) > 2 else ""
        upper_tol = row_data[3] if len(row_data) > 3 else ""
        lower_tol = row_data[4] if len(row_data) > 4 else ""

        # O列=C+D（尺寸+上公差）
        try:
            if dimension and upper_tol:
                try:
                    dim_value = float(dimension)
                    # 处理公差值，去掉前面的+号
                    tol_value = float(upper_tol.replace('+', '')) if upper_tol.startswith('+') else float(upper_tol)
                    # 计算结果
                    result_value = dim_value + tol_value
                    # 设置单元格值为计算结果
                    ws.range(f"O{row_idx}").value = result_value
                except ValueError:
                    # 如果无法转换为数值，则留空
                    ws.range(f"O{row_idx}").value = ""
            else:
                ws.range(f"O{row_idx}").value = ""
        except Exception as e:
            print(f"计算O列时出错: {e}")
            ws.range(f"O{row_idx}").value = ""

        # P列=C+E（尺寸+下公差）
        try:
            if dimension and lower_tol:
                try:
                    dim_value = float(dimension)
                    # 处理公差值，去掉前面的+号
                    tol_value = float(lower_tol.replace('+', '')) if lower_tol.startswith('+') else float(lower_tol)
                    # 计算结果
                    result_value = dim_value + tol_value
                    # 设置单元格值为计算结果
                    ws.range(f"P{row_idx}").value = result_value
                except ValueError:
                    # 如果无法转换为数值，则留空
                    ws.range(f"P{row_idx}").value = ""
            else:
                ws.range(f"P{row_idx}").value = ""
        except Exception as e:
            print(f"计算P列时出错: {e}")
            ws.range(f"P{row_idx}").value = ""

    def _export_with_openpyxl(self, template_path, save_path, annotations_data, start_row, insert_count):
        """使用openpyxl进行导出"""
        if not HAS_EXCEL_SUPPORT:
            QMessageBox.critical(self.main_window, "导出失败", "未找到可用的Excel处理库")
            return

        # 打开模板文件
        wb = openpyxl.load_workbook(template_path)
        ws = wb.active

        # 保存Q13-S30区域的内容
        q_s_content = {}
        for r in range(start_row, start_row + insert_count + 30):  # 保存足够多的行
            for c in range(17, 20):  # Q=17, R=18, S=19
                try:
                    cell_coord = f"{openpyxl.utils.get_column_letter(c)}{r}"
                    q_s_content[cell_coord] = ws[cell_coord].value
                except:
                    continue

        # 插入行
        ws.insert_rows(start_row, insert_count)

        # 检查并手动取消新插入行中的合并单元格
        for r in range(start_row, start_row + insert_count):
            # 检查每个单元格是否是合并单元格的一部分
            for c in range(1, 17):  # A列到P列
                try:
                    # 获取单元格坐标
                    coord = f"{openpyxl.utils.get_column_letter(c)}{r}"

                    # 检查该单元格是否是合并单元格的一部分
                    for merged_range in list(ws.merged_cells.ranges):
                        if coord in merged_range:
                            # 如果是合并单元格，解除合并
                            ws.unmerge_cells(str(merged_range))
                            break
                except:
                    continue

        # 恢复Q-S列的内容
        for cell_coord, value in q_s_content.items():
            ws[cell_coord] = value

        # 填充数据
        for i, row_data in enumerate(annotations_data):
            row_idx = start_row + i
            # 只处理A-E列的数据
            for j, value in enumerate(row_data):
                if j < len(row_data):  # 确保不越界
                    ws.cell(row=row_idx, column=j+1).value = value

            # 计算并填充O列和P列
            self._calculate_openpyxl_columns(ws, row_idx, row_data)

        # 保存文件
        wb.save(save_path)
        QMessageBox.information(
            self.main_window,
            "导出成功",
            f"标注列表已成功导出到:\n{save_path}"
        )
        self.main_window.status_bar.showMessage(f"成功导出到 {Path(save_path).name}", 5000)

    def _calculate_openpyxl_columns(self, ws, row_idx, row_data):
        """使用openpyxl计算并填充O列和P列的数据"""
        dimension = row_data[2] if len(row_data) > 2 else ""
        upper_tol = row_data[3] if len(row_data) > 3 else ""
        lower_tol = row_data[4] if len(row_data) > 4 else ""

        # O列=C+D（尺寸+上公差）
        try:
            if dimension and upper_tol:
                try:
                    dim_value = float(dimension)
                    # 处理公差值，去掉前面的+号
                    tol_value = float(upper_tol.replace('+', '')) if upper_tol.startswith('+') else float(upper_tol)
                    # 计算结果
                    result_value = dim_value + tol_value
                    # 设置单元格值为计算结果
                    ws.cell(row=row_idx, column=15).value = result_value  # O列是第15列
                except ValueError:
                    # 如果无法转换为数值，则留空
                    ws.cell(row=row_idx, column=15).value = ""
            else:
                ws.cell(row=row_idx, column=15).value = ""
        except Exception as e:
            print(f"计算O列时出错: {e}")
            ws.cell(row=row_idx, column=15).value = ""

        # P列=C+E（尺寸+下公差）
        try:
            if dimension and lower_tol:
                try:
                    dim_value = float(dimension)
                    # 处理公差值，去掉前面的+号
                    tol_value = float(lower_tol.replace('+', '')) if lower_tol.startswith('+') else float(lower_tol)
                    # 计算结果
                    result_value = dim_value + tol_value
                    # 设置单元格值为计算结果
                    ws.cell(row=row_idx, column=16).value = result_value  # P列是第16列
                except ValueError:
                    # 如果无法转换为数值，则留空
                    ws.cell(row=row_idx, column=16).value = ""
            else:
                ws.cell(row=row_idx, column=16).value = ""
        except Exception as e:
            print(f"计算P列时出错: {e}")
            ws.cell(row=row_idx, column=16).value = ""

    def export_annotated_image(self):
        """将当前视图（包含背景和所有标注）导出为图片"""
        if not self.main_window.current_pixmap:
            QMessageBox.warning(self.main_window, "无法导出", "当前没有加载任何图片。")
            return

        # 构造默认文件名
        if self.main_window.pdf_file_path:
            # PDF模式下使用原始PDF文件名
            pdf_name = Path(self.main_window.pdf_file_path).stem
            if hasattr(self.main_window, 'current_pdf_page') and self.main_window.pdf_page_count > 1:
                # 多页PDF，显示页码
                page_num = self.main_window.current_pdf_page + 1  # 页码从0开始，显示时+1
                default_filename = f"{pdf_name}_第{page_num}页_标注图片.png"
            else:
                # 单页PDF或页码信息不可用
                default_filename = f"{pdf_name}_标注图片.png"
        elif self.main_window.current_file_path:
            # 普通图片模式
            file_name = Path(self.main_window.current_file_path).stem
            default_filename = f"{file_name}_标注图片.png"
        else:
            # 没有文件路径信息
            default_filename = "标注图片.png"

        # 弹出文件保存对话框
        file_path, _ = QFileDialog.getSaveFileName(
            self.main_window,
            "导出标注图片",
            default_filename,
            "PNG 文件 (*.png);;JPEG 文件 (*.jpg *.jpeg)"
        )

        if not file_path:
            return

        try:
            # 调用GraphicsView中的导出方法
            success = self.main_window.graphics_view.export_as_image(file_path)

            if success:
                QMessageBox.information(
                    self.main_window,
                    "导出成功",
                    f"标注图片已成功保存到:\n{file_path}"
                )
                self.main_window.status_bar.showMessage(f"图片已导出到 {Path(file_path).name}", 5000)
            else:
                raise Exception("渲染或保存图像时发生未知错误。")

        except Exception as e:
            QMessageBox.critical(
                self.main_window,
                "导出失败",
                f"导出图片时发生错误:\n{e}"
            )
            self.main_window.status_bar.showMessage("图片导出失败", 3000)
